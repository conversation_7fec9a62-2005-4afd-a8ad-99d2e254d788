# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ui.py', '.'),
    ],
    hiddenimports=[
        'streamlit',
        'streamlit.web.cli',
        'streamlit.runtime.scriptrunner.script_runner',
        'streamlit.runtime.state',
        'streamlit.runtime.caching',
        'streamlit.runtime.uploaded_file_manager',
        'streamlit.components.v1.components',
        'streamlit.delta_generator',
        'streamlit.elements',
        'streamlit.elements.form',
        'streamlit.elements.widgets',
        'streamlit.elements.lib',
        'streamlit.elements.lib.column_config_utils',
        'streamlit.elements.lib.policies',
        'streamlit.proto',
        'streamlit.runtime.metrics_util',
        'streamlit.runtime.legacy_caching',
        'streamlit.runtime.forward_msg_queue',
        'streamlit.runtime.app_session',
        'streamlit.runtime.runtime',
        'streamlit.runtime.memory',
        'streamlit.runtime.stats',
        'streamlit.web.server',
        'streamlit.web.server.server',
        'streamlit.web.server.routes',
        'streamlit.web.server.media_file_handler',
        'streamlit.web.server.component_request_handler',
        'streamlit.web.server.upload_file_request_handler',
        'streamlit.web.server.health_handler',
        'streamlit.web.server.message_cache_handler',
        'streamlit.web.server.stats_request_handler',
        'streamlit.logger',
        'streamlit.config',
        'streamlit.file_util',
        'streamlit.source_util',
        'streamlit.string_util',
        'streamlit.type_util',
        'streamlit.util',
        'streamlit.watcher',
        'streamlit.folder_black_list',
        'streamlit.git_util',
        'streamlit.env_util',
        'streamlit.bootstrap',
        'streamlit.case_converters',
        'streamlit.cursor',
        'streamlit.deprecation_util',
        'streamlit.error_util',
        'streamlit.hash_util',
        'streamlit.in_memory_file_manager',
        'streamlit.report_thread',
        'streamlit.server.server_util',
        'streamlit.state.session_state_proxy',
        'streamlit.state.widgets',
        'streamlit.uploaded_file_manager',
        'streamlit.user_info',
        'streamlit.version',
        'streamlit.web.bootstrap',
        'pandas',
        'numpy',
        'altair',
        'plotly',
        'tornado',
        'tornado.web',
        'tornado.websocket',
        'tornado.ioloop',
        'tornado.httpserver',
        'tornado.netutil',
        'tornado.options',
        'tornado.log',
        'tornado.escape',
        'tornado.util',
        'tornado.concurrent',
        'tornado.gen',
        'tornado.locks',
        'tornado.queues',
        'tornado.iostream',
        'tornado.tcpserver',
        'tornado.process',
        'tornado.platform',
        'tornado.platform.auto',
        'tornado.routing',
        'tornado.auth',
        'tornado.locale',
        'tornado.template',
        'tornado.testing',
        'tornado.httpclient',
        'tornado.httputil',
        'tornado.simple_httpclient',
        'tornado.curl_httpclient',
        'tornado.wsgi',
        'click',
        'click.core',
        'click.decorators',
        'click.exceptions',
        'click.formatting',
        'click.options',
        'click.parser',
        'click.types',
        'click.utils',
        'toml',
        'validators',
        'packaging',
        'packaging.version',
        'packaging.specifiers',
        'packaging.requirements',
        'pyarrow',
        'pyarrow.lib',
        'pyarrow.parquet',
        'pyarrow.csv',
        'pyarrow.json',
        'pyarrow.feather',
        'pyarrow.orc',
        'pyarrow.dataset',
        'pyarrow.compute',
        'pyarrow.types',
        'pyarrow.schema',
        'pyarrow.table',
        'pyarrow.array',
        'pyarrow.scalar',
        'pyarrow.tensor',
        'pyarrow.plasma',
        'pyarrow.flight',
        'pyarrow.gandiva',
        'pyarrow.cuda',
        'pyarrow.fs',
        'pyarrow.hdfs',
        'pyarrow.s3fs',
        'pyarrow.gcsfs',
        'pyarrow.azurefs',
        'pyarrow.substrait',
        'pyarrow.acero',
        'pyarrow.interchange',
        'pyarrow.ipc',
        'pyarrow.memory',
        'pyarrow.util',
        'pyarrow.vendored',
        'pyarrow.vendored.version',
        'pyarrow._compute',
        'pyarrow._csv',
        'pyarrow._dataset',
        'pyarrow._feather',
        'pyarrow._flight',
        'pyarrow._fs',
        'pyarrow._gandiva',
        'pyarrow._hdfs',
        'pyarrow._json',
        'pyarrow._orc',
        'pyarrow._parquet',
        'pyarrow._plasma',
        'pyarrow._s3fs',
        'pyarrow._substrait',
        'pyarrow.lib',
        'blinker',
        'blinker.base',
        'blinker._utilities',
        'blinker._saferef',
        'protobuf',
        'google.protobuf',
        'google.protobuf.message',
        'google.protobuf.descriptor',
        'google.protobuf.descriptor_pb2',
        'google.protobuf.message_factory',
        'google.protobuf.reflection',
        'google.protobuf.service',
        'google.protobuf.service_reflection',
        'google.protobuf.text_format',
        'google.protobuf.json_format',
        'google.protobuf.util',
        'google.protobuf.util.json_format',
        'google.protobuf.util.message_util',
        'google.protobuf.util.type_util',
        'google.protobuf.compiler',
        'google.protobuf.compiler.plugin_pb2',
        'google.protobuf.internal',
        'google.protobuf.internal.api_implementation',
        'google.protobuf.internal.containers',
        'google.protobuf.internal.decoder',
        'google.protobuf.internal.encoder',
        'google.protobuf.internal.enum_type_wrapper',
        'google.protobuf.internal.extension_dict',
        'google.protobuf.internal.message_listener',
        'google.protobuf.internal.python_message',
        'google.protobuf.internal.type_checkers',
        'google.protobuf.internal.well_known_types',
        'google.protobuf.internal.wire_format',
        'google.protobuf.pyext',
        'google.protobuf.pyext._message',
        'google.protobuf.symbol_database',
        'google.protobuf.descriptor_database',
        'google.protobuf.descriptor_pool',
        'google.protobuf.message_factory',
        'google.protobuf.proto_builder',
        'google.protobuf.text_encoding',
        'google.protobuf.unknown_fields',
        'google.protobuf.wrappers_pb2',
        'google.protobuf.any_pb2',
        'google.protobuf.api_pb2',
        'google.protobuf.duration_pb2',
        'google.protobuf.empty_pb2',
        'google.protobuf.field_mask_pb2',
        'google.protobuf.source_context_pb2',
        'google.protobuf.struct_pb2',
        'google.protobuf.timestamp_pb2',
        'google.protobuf.type_pb2',
    ],
    hookspath=['./hooks'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='streamlit_app',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
