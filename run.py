import sys
import os
from ui import *
import streamlit.web.cli as stcli



def resolve_path(path):
    # 检查是否在PyInstaller打包环境中
    if getattr(sys, 'frozen', False):
        # 在打包环境中，使用程序所在目录
        # 使用getattr避免静态类型检查警告
        base_path = getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 在开发环境中，使用当前工作目录
        base_path = os.getcwd()

    resolved_path = os.path.abspath(os.path.join(base_path, path))
    return resolved_path


if __name__ == "__main__":
    sys.argv = [
        "streamlit",
        "run",
        resolve_path("ui.py"),
        "--global.developmentMode=false",
    ]
    sys.exit(stcli.main())