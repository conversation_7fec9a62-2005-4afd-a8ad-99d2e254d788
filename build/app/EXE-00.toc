('/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/dist/streamlit_app',
 True,
 False,
 False,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 'arm64',
 None,
 None,
 '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/streamlit_app.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('run',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/run.py',
   'PYSOURCE'),
  ('pyarrow/libarrow_python.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python.dylib',
   'BINARY'),
  ('pyarrow/libarrow.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_flight.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_flight.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_compute.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_compute.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2100.0.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_substrait.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_substrait.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_acero.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_acero.2100.dylib',
   'BINARY'),
  ('pyarrow/libparquet.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libparquet.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_dataset.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_dataset.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2100.0.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.dylib',
   'BINARY'),
  ('libpython3.13.dylib',
   '/opt/miniconda3/envs/dist/lib/libpython3.13.dylib',
   'BINARY'),
  ('pyarrow/lib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.cpython-313-darwin.so',
   'BINARY'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('google/_upb/_message.abi3.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/_upb/_message.abi3.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/hashing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/period.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslib.cpython-313-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/markupsafe/_speedups.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/ops.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/parsers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/window/indexers.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/indexing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/groupby.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/interval.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/index.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/hashtable.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/algos.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/arrays.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/sparse.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/internals.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_substrait.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_substrait.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_s3fs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_s3fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet_encryption.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_orc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_hdfs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_hdfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_gcsfs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_gcsfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_fs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_flight.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_flight.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_orc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_csv.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_compute.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_azurefs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_azurefs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_acero.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_acero.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_feather.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_feather.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/md__mypyc.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/md.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sqlite3.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/window/aggregations.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/join.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/reshape.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/properties.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_elementtree.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/writers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/mtrand.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/bit_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_sfc64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_philox.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_pcg64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_mt19937.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_common.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_bounded_integers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/missing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/pandas_parser.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/lib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/cmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/base.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/testing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/sas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/byteswap.cpython-313-darwin.so',
   'EXTENSION'),
  ('rpds/rpds.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/rpds/rpds.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_webp.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imagingtk.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_avif.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_avif.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imagingcms.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imagingmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imaging.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_testcapi.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_testcapi.cpython-313-darwin.so',
   'EXTENSION'),
  ('tornado/speedups.abi3.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/speedups.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('libmpdec.4.dylib',
   '/opt/miniconda3/envs/dist/lib/libmpdec.4.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/miniconda3/envs/dist/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libz.1.dylib', '/opt/miniconda3/envs/dist/lib/libz.1.dylib', 'BINARY'),
  ('libbz2.dylib', '/opt/miniconda3/envs/dist/lib/libbz2.dylib', 'BINARY'),
  ('libexpat.1.dylib',
   '/opt/miniconda3/envs/dist/lib/libexpat.1.dylib',
   'BINARY'),
  ('libssl.3.dylib', '/opt/miniconda3/envs/dist/lib/libssl.3.dylib', 'BINARY'),
  ('libffi.8.dylib', '/opt/miniconda3/envs/dist/lib/libffi.8.dylib', 'BINARY'),
  ('libreadline.8.dylib',
   '/opt/miniconda3/envs/dist/lib/libreadline.8.dylib',
   'BINARY'),
  ('libsqlite3.dylib',
   '/opt/miniconda3/envs/dist/lib/libsqlite3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libavif.16.3.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libavif.16.3.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libncursesw.6.dylib',
   'BINARY'),
  ('libncurses.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libncurses.6.dylib',
   'BINARY'),
  ('libicuuc.75.dylib',
   '/opt/miniconda3/envs/dist/lib/libicuuc.75.dylib',
   'BINARY'),
  ('libicui18n.75.dylib',
   '/opt/miniconda3/envs/dist/lib/libicui18n.75.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libtinfow.6.dylib',
   'BINARY'),
  ('libtinfo.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libtinfo.6.dylib',
   'BINARY'),
  ('libicudata.75.dylib',
   '/opt/miniconda3/envs/dist/lib/libicudata.75.dylib',
   'BINARY'),
  ('numpy-2.3.2.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info/LICENSE.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/LICENSE.txt',
   'DATA'),
  ('numpy-2.3.2.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/METADATA',
   'DATA'),
  ('numpy-2.3.2.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/RECORD',
   'DATA'),
  ('numpy-2.3.2.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/WHEEL',
   'DATA'),
  ('numpy-2.3.2.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/entry_points.txt',
   'DATA'),
  ('pandas-2.3.1.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas-2.3.1.dist-info/INSTALLER',
   'DATA'),
  ('pandas-2.3.1.dist-info/LICENSE',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas-2.3.1.dist-info/LICENSE',
   'DATA'),
  ('pandas-2.3.1.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas-2.3.1.dist-info/METADATA',
   'DATA'),
  ('pandas-2.3.1.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas-2.3.1.dist-info/RECORD',
   'DATA'),
  ('pandas-2.3.1.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas-2.3.1.dist-info/WHEEL',
   'DATA'),
  ('pandas-2.3.1.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas-2.3.1.dist-info/entry_points.txt',
   'DATA'),
  ('streamlit-1.47.1.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/INSTALLER',
   'DATA'),
  ('streamlit-1.47.1.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/METADATA',
   'DATA'),
  ('streamlit-1.47.1.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/RECORD',
   'DATA'),
  ('streamlit-1.47.1.dist-info/REQUESTED',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/REQUESTED',
   'DATA'),
  ('streamlit-1.47.1.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/WHEEL',
   'DATA'),
  ('streamlit-1.47.1.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/entry_points.txt',
   'DATA'),
  ('streamlit-1.47.1.dist-info/top_level.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit-1.47.1.dist-info/top_level.txt',
   'DATA'),
  ('streamlit/proto/Alert_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Alert_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AppPage_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AppPage_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ArrowNamedDataSet_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ArrowNamedDataSet_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ArrowVegaLiteChart_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ArrowVegaLiteChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Arrow_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Arrow_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AudioInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AudioInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Audio_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Audio_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AuthRedirect_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AuthRedirect_pb2.pyi',
   'DATA'),
  ('streamlit/proto/AutoRerun_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AutoRerun_pb2.pyi',
   'DATA'),
  ('streamlit/proto/BackMsg_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/BackMsg_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Balloons_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Balloons_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Block_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Block_pb2.pyi',
   'DATA'),
  ('streamlit/proto/BokehChart_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/BokehChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ButtonGroup_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ButtonGroup_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Button_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Button_pb2.pyi',
   'DATA'),
  ('streamlit/proto/CameraInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/CameraInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ChatInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ChatInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Checkbox_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Checkbox_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ClientState_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ClientState_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Code_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Code_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ColorPicker_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ColorPicker_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Common_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Common_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Components_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Components_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DataFrame_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DataFrame_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DateInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DateInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DeckGlJsonChart_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DeckGlJsonChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Delta_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Delta_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DocString_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DocString_pb2.pyi',
   'DATA'),
  ('streamlit/proto/DownloadButton_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DownloadButton_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Element_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Element_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Empty_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Empty_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Exception_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Exception_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Favicon_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Favicon_pb2.pyi',
   'DATA'),
  ('streamlit/proto/FileUploader_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/FileUploader_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ForwardMsg_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ForwardMsg_pb2.pyi',
   'DATA'),
  ('streamlit/proto/GapSize_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GapSize_pb2.pyi',
   'DATA'),
  ('streamlit/proto/GitInfo_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GitInfo_pb2.pyi',
   'DATA'),
  ('streamlit/proto/GraphVizChart_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GraphVizChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Heading_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Heading_pb2.pyi',
   'DATA'),
  ('streamlit/proto/HeightConfig_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/HeightConfig_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Html_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Html_pb2.pyi',
   'DATA'),
  ('streamlit/proto/IFrame_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/IFrame_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Image_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Image_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Json_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Json_pb2.pyi',
   'DATA'),
  ('streamlit/proto/LabelVisibilityMessage_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/LabelVisibilityMessage_pb2.pyi',
   'DATA'),
  ('streamlit/proto/LinkButton_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/LinkButton_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Logo_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Logo_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Markdown_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Markdown_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Metric_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Metric_pb2.pyi',
   'DATA'),
  ('streamlit/proto/MetricsEvent_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/MetricsEvent_pb2.pyi',
   'DATA'),
  ('streamlit/proto/MultiSelect_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/MultiSelect_pb2.pyi',
   'DATA'),
  ('streamlit/proto/NamedDataSet_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NamedDataSet_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Navigation_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Navigation_pb2.pyi',
   'DATA'),
  ('streamlit/proto/NewSession_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NewSession_pb2.pyi',
   'DATA'),
  ('streamlit/proto/NumberInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NumberInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageConfig_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageConfig_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageInfo_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageInfo_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageLink_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageLink_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageNotFound_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageNotFound_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PageProfile_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageProfile_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PagesChanged_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PagesChanged_pb2.pyi',
   'DATA'),
  ('streamlit/proto/ParentMessage_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ParentMessage_pb2.pyi',
   'DATA'),
  ('streamlit/proto/PlotlyChart_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PlotlyChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Progress_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Progress_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Radio_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Radio_pb2.pyi',
   'DATA'),
  ('streamlit/proto/RootContainer_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/RootContainer_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Selectbox_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Selectbox_pb2.pyi',
   'DATA'),
  ('streamlit/proto/SessionEvent_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/SessionEvent_pb2.pyi',
   'DATA'),
  ('streamlit/proto/SessionStatus_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/SessionStatus_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Skeleton_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Skeleton_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Slider_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Slider_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Snow_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Snow_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Spinner_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Spinner_pb2.pyi',
   'DATA'),
  ('streamlit/proto/TextArea_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TextArea_pb2.pyi',
   'DATA'),
  ('streamlit/proto/TextInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TextInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Text_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Text_pb2.pyi',
   'DATA'),
  ('streamlit/proto/TimeInput_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TimeInput_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Toast_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Toast_pb2.pyi',
   'DATA'),
  ('streamlit/proto/VegaLiteChart_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/VegaLiteChart_pb2.pyi',
   'DATA'),
  ('streamlit/proto/Video_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Video_pb2.pyi',
   'DATA'),
  ('streamlit/proto/WidgetStates_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/WidgetStates_pb2.pyi',
   'DATA'),
  ('streamlit/proto/WidthConfig_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/WidthConfig_pb2.pyi',
   'DATA'),
  ('streamlit/proto/openmetrics_data_model_pb2.pyi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/openmetrics_data_model_pb2.pyi',
   'DATA'),
  ('streamlit/py.typed',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/py.typed',
   'DATA'),
  ('streamlit/static/favicon.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/favicon.png',
   'DATA'),
  ('streamlit/static/index.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/index.html',
   'DATA'),
  ('streamlit/static/manifest.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/manifest.json',
   'DATA'),
  ('streamlit/static/static/css/index.CJVRHjQZ.css',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/css/index.CJVRHjQZ.css',
   'DATA'),
  ('streamlit/static/static/css/index.CsLB_Bnz.css',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/css/index.CsLB_Bnz.css',
   'DATA'),
  ('streamlit/static/static/css/index.DzuxGC_t.css',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/css/index.DzuxGC_t.css',
   'DATA'),
  ('streamlit/static/static/js/ErrorOutline.esm.BEZPMjuG.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/ErrorOutline.esm.BEZPMjuG.js',
   'DATA'),
  ('streamlit/static/static/js/FileDownload.esm.Dy1V9a2E.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/FileDownload.esm.Dy1V9a2E.js',
   'DATA'),
  ('streamlit/static/static/js/FileHelper.D0K06YBq.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/FileHelper.D0K06YBq.js',
   'DATA'),
  ('streamlit/static/static/js/FormClearHelper.Cdw5Y7_m.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/FormClearHelper.Cdw5Y7_m.js',
   'DATA'),
  ('streamlit/static/static/js/Hooks.C_qx1sSw.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/Hooks.C_qx1sSw.js',
   'DATA'),
  ('streamlit/static/static/js/InputInstructions.D3IDU-eY.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/InputInstructions.D3IDU-eY.js',
   'DATA'),
  ('streamlit/static/static/js/ProgressBar.EhJ_lCOf.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/ProgressBar.EhJ_lCOf.js',
   'DATA'),
  ('streamlit/static/static/js/RenderInPortalIfExists.D6a0mMll.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/RenderInPortalIfExists.D6a0mMll.js',
   'DATA'),
  ('streamlit/static/static/js/Toolbar.D6yqQ65-.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/Toolbar.D6yqQ65-.js',
   'DATA'),
  ('streamlit/static/static/js/UploadFileInfo.C-jY39rj.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/UploadFileInfo.C-jY39rj.js',
   'DATA'),
  ('streamlit/static/static/js/base-input.DMlw5p7n.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/base-input.DMlw5p7n.js',
   'DATA'),
  ('streamlit/static/static/js/checkbox.C7QR6llE.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/checkbox.C7QR6llE.js',
   'DATA'),
  ('streamlit/static/static/js/createDownloadLinkElement.DZMwyjvU.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/createDownloadLinkElement.DZMwyjvU.js',
   'DATA'),
  ('streamlit/static/static/js/createSuper.C5k_2vfB.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/createSuper.C5k_2vfB.js',
   'DATA'),
  ('streamlit/static/static/js/data-grid-overlay-editor.CoquyZNK.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/data-grid-overlay-editor.CoquyZNK.js',
   'DATA'),
  ('streamlit/static/static/js/downloader.Nj6v3ioB.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/downloader.Nj6v3ioB.js',
   'DATA'),
  ('streamlit/static/static/js/es6.CVz13CSz.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/es6.CVz13CSz.js',
   'DATA'),
  ('streamlit/static/static/js/iframeResizer.contentWindow.gZ8zjT0K.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/iframeResizer.contentWindow.gZ8zjT0K.js',
   'DATA'),
  ('streamlit/static/static/js/index.B2L574n6.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.B2L574n6.js',
   'DATA'),
  ('streamlit/static/static/js/index.B3n-pURl.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.B3n-pURl.js',
   'DATA'),
  ('streamlit/static/static/js/index.B9jJp9aE.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.B9jJp9aE.js',
   'DATA'),
  ('streamlit/static/static/js/index.BBVtld-D.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.BBVtld-D.js',
   'DATA'),
  ('streamlit/static/static/js/index.BKHPnvYd.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.BKHPnvYd.js',
   'DATA'),
  ('streamlit/static/static/js/index.BSFzxMXi.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.BSFzxMXi.js',
   'DATA'),
  ('streamlit/static/static/js/index.B_1CXynz.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.B_1CXynz.js',
   'DATA'),
  ('streamlit/static/static/js/index.Bm1LklYO.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.Bm1LklYO.js',
   'DATA'),
  ('streamlit/static/static/js/index.BscWuWHL.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.BscWuWHL.js',
   'DATA'),
  ('streamlit/static/static/js/index.Byi6__iF.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.Byi6__iF.js',
   'DATA'),
  ('streamlit/static/static/js/index.BzJeMpQ-.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.BzJeMpQ-.js',
   'DATA'),
  ('streamlit/static/static/js/index.C4tw7-Cl.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.C4tw7-Cl.js',
   'DATA'),
  ('streamlit/static/static/js/index.CG0C49ex.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CG0C49ex.js',
   'DATA'),
  ('streamlit/static/static/js/index.CMuSJPv-.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CMuSJPv-.js',
   'DATA'),
  ('streamlit/static/static/js/index.COPFcr_K.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.COPFcr_K.js',
   'DATA'),
  ('streamlit/static/static/js/index.CTT2YqEU.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CTT2YqEU.js',
   'DATA'),
  ('streamlit/static/static/js/index.CU3TLDlu.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CU3TLDlu.js',
   'DATA'),
  ('streamlit/static/static/js/index.CVKKDwaf.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CVKKDwaf.js',
   'DATA'),
  ('streamlit/static/static/js/index.CWZeK3mV.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CWZeK3mV.js',
   'DATA'),
  ('streamlit/static/static/js/index.CWbiNJQl.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CWbiNJQl.js',
   'DATA'),
  ('streamlit/static/static/js/index.CWxefYP6.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CWxefYP6.js',
   'DATA'),
  ('streamlit/static/static/js/index.CbQtRkVt.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.CbQtRkVt.js',
   'DATA'),
  ('streamlit/static/static/js/index.D3wOJJsg.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.D3wOJJsg.js',
   'DATA'),
  ('streamlit/static/static/js/index.DOdWa88b.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.DOdWa88b.js',
   'DATA'),
  ('streamlit/static/static/js/index.DXtnaPua.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.DXtnaPua.js',
   'DATA'),
  ('streamlit/static/static/js/index.DatDwFl3.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.DatDwFl3.js',
   'DATA'),
  ('streamlit/static/static/js/index.DeWYPvQR.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.DeWYPvQR.js',
   'DATA'),
  ('streamlit/static/static/js/index.Do4vzIvK.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.Do4vzIvK.js',
   'DATA'),
  ('streamlit/static/static/js/index.Dvrstlh8.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.Dvrstlh8.js',
   'DATA'),
  ('streamlit/static/static/js/index.DvznfdF_.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.DvznfdF_.js',
   'DATA'),
  ('streamlit/static/static/js/index.MbqsiUV4.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.MbqsiUV4.js',
   'DATA'),
  ('streamlit/static/static/js/index.U1vvXeGn.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.U1vvXeGn.js',
   'DATA'),
  ('streamlit/static/static/js/index._WAuWRkp.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index._WAuWRkp.js',
   'DATA'),
  ('streamlit/static/static/js/index.bwA9_eWC.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.bwA9_eWC.js',
   'DATA'),
  ('streamlit/static/static/js/index.m1njXuKl.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.m1njXuKl.js',
   'DATA'),
  ('streamlit/static/static/js/index.mihWZKb1.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.mihWZKb1.js',
   'DATA'),
  ('streamlit/static/static/js/index.xfcNJBLM.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/index.xfcNJBLM.js',
   'DATA'),
  ('streamlit/static/static/js/input.D_45B0P-.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/input.D_45B0P-.js',
   'DATA'),
  ('streamlit/static/static/js/inputUtils.CptNuJwn.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/inputUtils.CptNuJwn.js',
   'DATA'),
  ('streamlit/static/static/js/memory.BmhrRyO2.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/memory.BmhrRyO2.js',
   'DATA'),
  ('streamlit/static/static/js/mergeWith.DvOME7eH.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/mergeWith.DvOME7eH.js',
   'DATA'),
  ('streamlit/static/static/js/number-overlay-editor.BRNxOzEZ.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/number-overlay-editor.BRNxOzEZ.js',
   'DATA'),
  ('streamlit/static/static/js/possibleConstructorReturn.C5GiK_Ob.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/possibleConstructorReturn.C5GiK_Ob.js',
   'DATA'),
  ('streamlit/static/static/js/sandbox.B-Q9S7vW.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/sandbox.B-Q9S7vW.js',
   'DATA'),
  ('streamlit/static/static/js/sprintf.D7DtBTRn.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/sprintf.D7DtBTRn.js',
   'DATA'),
  ('streamlit/static/static/js/threshold.DjX0wlsa.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/threshold.DjX0wlsa.js',
   'DATA'),
  ('streamlit/static/static/js/timepicker.DOMbfm_a.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/timepicker.DOMbfm_a.js',
   'DATA'),
  ('streamlit/static/static/js/timer.CAwTRJ_g.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/timer.CAwTRJ_g.js',
   'DATA'),
  ('streamlit/static/static/js/toConsumableArray.CbNz0Ciu.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/toConsumableArray.CbNz0Ciu.js',
   'DATA'),
  ('streamlit/static/static/js/uniqueId.-ygIU7IL.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/uniqueId.-ygIU7IL.js',
   'DATA'),
  ('streamlit/static/static/js/useBasicWidgetState.Bx3VaRHk.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/useBasicWidgetState.Bx3VaRHk.js',
   'DATA'),
  ('streamlit/static/static/js/useOnInputChange.CDZx-L6q.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/useOnInputChange.CDZx-L6q.js',
   'DATA'),
  ('streamlit/static/static/js/useTextInputAutoExpand.BuE9l5TG.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/useTextInputAutoExpand.BuE9l5TG.js',
   'DATA'),
  ('streamlit/static/static/js/value.CgPGBV_l.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/value.CgPGBV_l.js',
   'DATA'),
  ('streamlit/static/static/js/withFullScreenWrapper.DWXejOhQ.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/js/withFullScreenWrapper.DWXejOhQ.js',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_AMS-Regular.BQhdFMY1.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_AMS-Regular.BQhdFMY1.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_AMS-Regular.DMm9YOAa.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_AMS-Regular.DMm9YOAa.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_AMS-Regular.DRggAlZN.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_AMS-Regular.DRggAlZN.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Bold.ATXxdsX0.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Bold.ATXxdsX0.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Bold.BEiXGLvX.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Bold.BEiXGLvX.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Regular.CTRA-rTL.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Regular.CTRA-rTL.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Regular.Di6jR-x-.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Regular.Di6jR-x-.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Caligraphic-Regular.wX97UBjC.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Caligraphic-Regular.wX97UBjC.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Bold.BdnERNNW.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Bold.BdnERNNW.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Bold.BsDP51OF.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Bold.BsDP51OF.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Bold.CL6g_b3V.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Bold.CL6g_b3V.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Regular.CB_wures.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Regular.CB_wures.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Regular.CTYiF6lA.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Regular.CTYiF6lA.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Fraktur-Regular.Dxdc4cR9.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Fraktur-Regular.Dxdc4cR9.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Bold.Cx986IdX.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Bold.Cx986IdX.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Bold.Jm3AIy58.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Bold.Jm3AIy58.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Bold.waoOVXN0.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Bold.waoOVXN0.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-BoldItalic.DxDJ3AOS.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-BoldItalic.DxDJ3AOS.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-BoldItalic.DzxPMmG6.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-BoldItalic.DzxPMmG6.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-BoldItalic.SpSLRI95.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-BoldItalic.SpSLRI95.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Italic.3WenGoN9.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Italic.3WenGoN9.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Italic.BMLOBm91.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Italic.BMLOBm91.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Italic.NWA7e6Wa.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Italic.NWA7e6Wa.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Regular.B22Nviop.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Regular.B22Nviop.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Regular.Dr94JaBh.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Regular.Dr94JaBh.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Main-Regular.ypZvNtVU.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Main-Regular.ypZvNtVU.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-BoldItalic.B3XSjfu4.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-BoldItalic.B3XSjfu4.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-BoldItalic.CZnvNsCZ.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-BoldItalic.CZnvNsCZ.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-BoldItalic.iY-2wyZ7.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-BoldItalic.iY-2wyZ7.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-Italic.DA0__PXp.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-Italic.DA0__PXp.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-Italic.flOr_0UB.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-Italic.flOr_0UB.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Math-Italic.t53AETM-.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Math-Italic.t53AETM-.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Bold.CFMepnvq.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Bold.CFMepnvq.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Bold.D1sUS0GD.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Bold.D1sUS0GD.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Bold.DbIhKOiC.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Bold.DbIhKOiC.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Italic.C3H0VqGB.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Italic.C3H0VqGB.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Italic.DN2j7dab.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Italic.DN2j7dab.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Italic.YYjJ1zSn.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Italic.YYjJ1zSn.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Regular.BNo7hRIc.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Regular.BNo7hRIc.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Regular.CS6fqUqJ.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Regular.CS6fqUqJ.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_SansSerif-Regular.DDBCnlJ7.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_SansSerif-Regular.DDBCnlJ7.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Script-Regular.C5JkGWo-.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Script-Regular.C5JkGWo-.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Script-Regular.D3wIWfF6.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Script-Regular.D3wIWfF6.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Script-Regular.D5yQViql.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Script-Regular.D5yQViql.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size1-Regular.C195tn64.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size1-Regular.C195tn64.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size1-Regular.Dbsnue_I.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size1-Regular.Dbsnue_I.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size1-Regular.mCD8mA8B.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size1-Regular.mCD8mA8B.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size2-Regular.B7gKUWhC.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size2-Regular.B7gKUWhC.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size2-Regular.Dy4dx90m.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size2-Regular.Dy4dx90m.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size2-Regular.oD1tc_U0.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size2-Regular.oD1tc_U0.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size3-Regular.CTq5MqoE.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size3-Regular.CTq5MqoE.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size3-Regular.DgpXs0kz.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size3-Regular.DgpXs0kz.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size4-Regular.BF-4gkZK.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size4-Regular.BF-4gkZK.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size4-Regular.DWFBv043.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size4-Regular.DWFBv043.ttf',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Size4-Regular.Dl5lxZxV.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Size4-Regular.Dl5lxZxV.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Typewriter-Regular.C0xS9mPB.woff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Typewriter-Regular.C0xS9mPB.woff',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Typewriter-Regular.CO6r4hn1.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Typewriter-Regular.CO6r4hn1.woff2',
   'DATA'),
  ('streamlit/static/static/media/KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf',
   'DATA'),
  ('streamlit/static/static/media/MaterialSymbols-Rounded.DsbC8sYI.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/MaterialSymbols-Rounded.DsbC8sYI.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceCodeVF-Italic.ttf.Ba1oaZG1.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/SourceCodeVF-Italic.ttf.Ba1oaZG1.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceCodeVF-Upright.ttf.BjWn63N-.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/SourceCodeVF-Upright.ttf.BjWn63N-.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSansVF-Italic.ttf.Bt9VkdQ3.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/SourceSansVF-Italic.ttf.Bt9VkdQ3.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSansVF-Upright.ttf.BsWL4Kly.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/SourceSansVF-Upright.ttf.BsWL4Kly.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSerifVariable-Italic.ttf.CVdzAtxO.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/SourceSerifVariable-Italic.ttf.CVdzAtxO.woff2',
   'DATA'),
  ('streamlit/static/static/media/SourceSerifVariable-Roman.ttf.mdpVL9bi.woff2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/SourceSerifVariable-Roman.ttf.mdpVL9bi.woff2',
   'DATA'),
  ('streamlit/static/static/media/balloon-0.Czj7AKwE.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/balloon-0.Czj7AKwE.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-1.CNvFFrND.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/balloon-1.CNvFFrND.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-2.DTvC6B1t.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/balloon-2.DTvC6B1t.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-3.CgSk4tbL.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/balloon-3.CgSk4tbL.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-4.mbtFrzxf.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/balloon-4.mbtFrzxf.png',
   'DATA'),
  ('streamlit/static/static/media/balloon-5.CSwkUfRA.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/balloon-5.CSwkUfRA.png',
   'DATA'),
  ('streamlit/static/static/media/fireworks.B4d-_KUe.gif',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/fireworks.B4d-_KUe.gif',
   'DATA'),
  ('streamlit/static/static/media/flake-0.DgWaVvm5.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/flake-0.DgWaVvm5.png',
   'DATA'),
  ('streamlit/static/static/media/flake-1.B2r5AHMK.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/flake-1.B2r5AHMK.png',
   'DATA'),
  ('streamlit/static/static/media/flake-2.BnWSExPC.png',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/flake-2.BnWSExPC.png',
   'DATA'),
  ('streamlit/static/static/media/snowflake.JU2jBHL8.svg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/static/static/media/snowflake.JU2jBHL8.svg',
   'DATA'),
  ('ui.py',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/ui.py',
   'DATA'),
  ('pyarrow/include/arrow/util/compare.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/compare.h',
   'DATA'),
  ('pyarrow/include/arrow/util/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_traits.h',
   'DATA'),
  ('pyarrow/tensor.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tensor.pxi',
   'DATA'),
  ('pyarrow/include/parquet/file_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/file_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/test_nodes.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/test_nodes.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/io/file.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/file.h',
   'DATA'),
  ('pyarrow/_parquet_encryption.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/async.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/async.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/tensor/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/util/list_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/list_util.h',
   'DATA'),
  ('pyarrow/includes/__init__.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/__init__.pxd',
   'DATA'),
  ('pyarrow/_gcsfs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_gcsfs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/flight/types_async.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types_async.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_data_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_data_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/time_series_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/time_series_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/stdio.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/stdio.h',
   'DATA'),
  ('pyarrow/include/arrow/util/compression.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/compression.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/tpch_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/tpch_node.h',
   'DATA'),
  ('pyarrow/include/arrow/util/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/visibility.h',
   'DATA'),
  ('pyarrow/include/parquet/api/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_dict.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_dict.h',
   'DATA'),
  ('pyarrow/include/arrow/python/decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/bloom_filter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/bloom_filter.h',
   'DATA'),
  ('pyarrow/include/parquet/level_conversion_inc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_conversion_inc.h',
   'DATA'),
  ('pyarrow/include/arrow/builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/python/arrow_to_pandas.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack_abi.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack_abi.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_flight_server.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_flight_server.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_auth_handlers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_auth_handlers.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/crc32.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/crc32.h',
   'DATA'),
  ('pyarrow/includes/libarrow_cuda.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/macros.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/macros.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_auth.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_auth.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow/include/arrow/util/io_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/io_util.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/crypto_factory.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/crypto_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/util/endian.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/endian.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   'DATA'),
  ('pyarrow/src/arrow/python/common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_init.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/include/arrow/buffer_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/buffer_builder.h',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.cc',
   'DATA'),
  ('pyarrow/_dataset_parquet.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/byte_size.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/byte_size.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/schema_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/schema_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/util/regex.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/regex.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hash_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/hash_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_node.h',
   'DATA'),
  ('pyarrow/include/parquet/column_page.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_page.h',
   'DATA'),
  ('pyarrow/include/arrow/util/simd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/simd.h',
   'DATA'),
  ('pyarrow/include/arrow/type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/type.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_compat.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_compat.h',
   'DATA'),
  ('pyarrow/_parquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/exec_plan.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/exec_plan.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_internal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/util/align_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/align_util.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_definitions.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_definitions.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/writer.h',
   'DATA'),
  ('pyarrow/_cuda.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/tracing.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/tracing.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.cc',
   'DATA'),
  ('pyarrow/public-api.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/public-api.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cpu_info.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/cpu_info.h',
   'DATA'),
  ('pyarrow/include/arrow/python/helpers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/kms_client_factory.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/kms_client_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/device.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/device.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/kms_client.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/kms_client.h',
   'DATA'),
  ('pyarrow/include/arrow/util/math_constants.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/math_constants.h',
   'DATA'),
  ('pyarrow/include/arrow/io/concurrency.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/concurrency.h',
   'DATA'),
  ('pyarrow/include/parquet/api/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/test_util.h',
   'DATA'),
  ('pyarrow/includes/libparquet_encryption.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/matchers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/matchers.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/functional.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/functional.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/generator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/generator.h',
   'DATA'),
  ('pyarrow/error.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/error.pxi',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_base.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_base.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/plan.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/plan.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   'DATA'),
  ('pyarrow/_dlpack.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dlpack.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/pcg_random.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/pcg_random.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/process.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/process.h',
   'DATA'),
  ('pyarrow/include/arrow/python/python_test.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/include/parquet/api/io.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/io.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ubsan.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/ubsan.h',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow/_parquet.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/localfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/localfs.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api.h',
   'DATA'),
  ('pyarrow/device.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/device.pxi',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_ipc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/python/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/date.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/date.h',
   'DATA'),
  ('pyarrow/include/parquet/properties.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/properties.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_material.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_material.h',
   'DATA'),
  ('pyarrow/include/arrow/python/flight.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/include/arrow/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3fs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3fs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_wrapper.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_wrapper.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/asof_join_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/asof_join_node.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_block_counter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_block_counter.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/order_by_impl.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/order_by_impl.h',
   'DATA'),
  ('pyarrow/include/arrow/util/union_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/union_util.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_builders.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_builders.h',
   'DATA'),
  ('pyarrow/include/arrow/chunked_array.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/chunked_array.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/projector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/projector.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_python_internal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow/_acero.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_acero.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_auth.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_auth.h',
   'DATA'),
  ('pyarrow/include/parquet/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3_test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_binary.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/checked_cast.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/checked_cast.h',
   'DATA'),
  ('pyarrow/_fs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_fs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/dataset/discovery.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/discovery.h',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/record_batch.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/record_batch.h',
   'DATA'),
  ('pyarrow/include/parquet/level_comparison_inc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_comparison_inc.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_compatibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_compatibility.h',
   'DATA'),
  ('pyarrow/include/arrow/io/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/api.h',
   'DATA'),
  ('pyarrow/tests/extensions.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/extensions.pyx',
   'DATA'),
  ('pyarrow/include/arrow/status.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/status.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/cast.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/cast.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/mutex.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/mutex.h',
   'DATA'),
  ('pyarrow/include/arrow/python/ipc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/thread_pool.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/thread_pool.h',
   'DATA'),
  ('pyarrow/include/arrow/python/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/lib.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.pxd',
   'DATA'),
  ('pyarrow/include/arrow/flight/client.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client.h',
   'DATA'),
  ('pyarrow/include/arrow/util/range.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/range.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/scanner.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/scanner.h',
   'DATA'),
  ('pyarrow/src/arrow/python/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/python/common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/partition_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/partition_util.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/uniform_real.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/uniform_real.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   'DATA'),
  ('pyarrow/include/arrow/array/diff.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/diff.h',
   'DATA'),
  ('pyarrow/gandiva.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/gandiva.pyx',
   'DATA'),
  ('pyarrow/include/parquet/schema.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/schema.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_run_end.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_run_end.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_ops.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_ops.h',
   'DATA'),
  ('pyarrow/include/arrow/json/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_tracing_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/parquet/parquet_version.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/parquet_version.h',
   'DATA'),
  ('pyarrow/include/arrow/util/uri.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/uri.h',
   'DATA'),
  ('pyarrow/include/parquet/windows_compatibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/windows_compatibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/aligned_storage.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/aligned_storage.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/visibility.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.cc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/path_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/path_util.h',
   'DATA'),
  ('pyarrow/include/parquet/level_conversion.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_conversion.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/query_context.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/query_context.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ree_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/ree_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/interfaces.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/interfaces.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/row/grouper.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/row/grouper.h',
   'DATA'),
  ('pyarrow/_csv.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_csv.pyx',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/mockfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/mockfs.h',
   'DATA'),
  ('pyarrow/include/arrow/array.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array.h',
   'DATA'),
  ('pyarrow/include/arrow/json/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   'DATA'),
  ('pyarrow/include/arrow/json/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util_overflow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util_overflow.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/serde.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/serde.h',
   'DATA'),
  ('pyarrow/include/arrow/util/decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/util/vector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/vector.h',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.cc',
   'DATA'),
  ('pyarrow/include/arrow/io/slow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/slow.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/test_encryption_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/test_encryption_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.cc',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_toolkit.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_toolkit.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_visit.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_visit.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem_library.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem_library.h',
   'DATA'),
  ('pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/fixed_width_test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/fixed_width_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_csv.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_csv.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.cc',
   'DATA'),
  ('pyarrow/include/arrow/testing/executor_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/executor_util.h',
   'DATA'),
  ('pyarrow/include/arrow/json/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/message.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/message.h',
   'DATA'),
  ('pyarrow/_dataset_parquet_encryption.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/_parquet_encryption.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow/include/arrow/json/chunked_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunked_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_metadata.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cancel.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/cancel.h',
   'DATA'),
  ('pyarrow/_dataset.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset.pxd',
   'DATA'),
  ('pyarrow/include/parquet/size_statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/size_statistics.h',
   'DATA'),
  ('pyarrow/io.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/io.pxi',
   'DATA'),
  ('pyarrow/include/parquet/page_index.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/page_index.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/schema.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/schema.h',
   'DATA'),
  ('pyarrow/include/parquet/benchmark_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/benchmark_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/include/arrow/io/transform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/transform.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/array/validate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/validate.h',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/otel_logging.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/otel_logging.h',
   'DATA'),
  ('pyarrow/builder.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/builder.pxi',
   'DATA'),
  ('pyarrow/include/arrow/engine/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/api.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   'DATA'),
  ('pyarrow/_hdfs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_hdfs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_parquet.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_parquet.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/future_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/future_util.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_scalar_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_scalar_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/utils.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/utils.h',
   'DATA'),
  ('pyarrow/include/arrow/util/prefetch.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/prefetch.h',
   'DATA'),
  ('pyarrow/scalar.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/scalar.pxi',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_vector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/registry.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/registry.h',
   'DATA'),
  ('pyarrow/src/arrow/python/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/util/config.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/config.h',
   'DATA'),
  ('pyarrow/include/arrow/table_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/table_builder.h',
   'DATA'),
  ('pyarrow/_cuda.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_cuda.pyx',
   'DATA'),
  ('pyarrow/include/arrow/testing/builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_decoder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_decoder.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/bool8.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/bool8.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/sparse_tensor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/sparse_tensor.h',
   'DATA'),
  ('pyarrow/src/arrow/python/iterators.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_cookie_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_cookie_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime.h',
   'DATA'),
  ('pyarrow/include/parquet/hasher.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/hasher.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/dictionary.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/dictionary.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/writer.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_unwrapper.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_unwrapper.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_dict.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_dict.h',
   'DATA'),
  ('pyarrow/_orc.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_orc.pyx',
   'DATA'),
  ('pyarrow/include/arrow/flight/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/api.h',
   'DATA'),
  ('pyarrow/_dataset_parquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/ios.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/ios.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/accumulation_queue.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/accumulation_queue.h',
   'DATA'),
  ('pyarrow/include/arrow/chunk_resolver.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/chunk_resolver.h',
   'DATA'),
  ('pyarrow/include/arrow/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/type_traits.h',
   'DATA'),
  ('pyarrow/include/parquet/level_comparison.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_comparison.h',
   'DATA'),
  ('pyarrow/include/parquet/types.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/types.h',
   'DATA'),
  ('pyarrow/src/arrow/python/util.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/util.cc',
   'DATA'),
  ('pyarrow/include/arrow/python/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator.h',
   'DATA'),
  ('pyarrow/compat.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/compat.pxi',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/options.h',
   'DATA'),
  ('pyarrow/include/arrow/util/task_group.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/task_group.h',
   'DATA'),
  ('pyarrow/include/arrow/compare.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compare.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/json/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/api.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow/includes/libarrow_python.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_python.pxd',
   'DATA'),
  ('pyarrow/include/arrow/csv/invalid_row.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/invalid_row.h',
   'DATA'),
  ('pyarrow/include/arrow/datum.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/datum.h',
   'DATA'),
  ('pyarrow/lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.h',
   'DATA'),
  ('pyarrow/include/arrow/util/basic_decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/basic_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/aggregate_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/aggregate_node.h',
   'DATA'),
  ('pyarrow/_json.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_json.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_nested.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_nested.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool_test.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool_test.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.cc',
   'DATA'),
  ('pyarrow/array.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/array.pxi',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/azurefs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/azurefs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport_server.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport_server.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_types.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_types.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/map_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/map_node.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/writer.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/arrow/python/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/_csv.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_csv.pxd',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.parquet',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/unreachable.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/unreachable.h',
   'DATA'),
  ('pyarrow/_flight.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_flight.pyx',
   'DATA'),
  ('pyarrow/include/arrow/device_allocation_type_set.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/device_allocation_type_set.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_nested.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_nested.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_generate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_generate.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/gcsfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/gcsfs.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/io/hdfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/hdfs.h',
   'DATA'),
  ('pyarrow/include/arrow/python/gdb.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_time.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_time.h',
   'DATA'),
  ('pyarrow/include/arrow/io/memory.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/memory.h',
   'DATA'),
  ('pyarrow/include/arrow/util/utf8.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/utf8.h',
   'DATA'),
  ('pyarrow/include/arrow/util/queue.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/queue.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/api.h',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join.h',
   'DATA'),
  ('pyarrow/tests/bound_function_visit_strings.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow/include/arrow/io/caching.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/caching.h',
   'DATA'),
  ('pyarrow/include/arrow/io/compressed.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/compressed.h',
   'DATA'),
  ('pyarrow/_azurefs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_azurefs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/array/statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/statistics.h',
   'DATA'),
  ('pyarrow/include/parquet/encoding.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encoding.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor_generate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visitor_generate.h',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/parquet/encryption/local_wrap_kms_client.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/string.h',
   'DATA'),
  ('pyarrow/includes/libarrow_fs.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_fs.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/rows_to_batches.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/rows_to_batches.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_scalar.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/result.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/result.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow/include/arrow/testing/async_test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/async_test_util.h',
   'DATA'),
  ('pyarrow/_compute.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute.pxd',
   'DATA'),
  ('pyarrow/include/arrow/c/bridge.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/bridge.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/chunker.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/chunker.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_tracing_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/exec.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/exec.h',
   'DATA'),
  ('pyarrow/include/arrow/scalar.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hashing.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/hashing.h',
   'DATA'),
  ('pyarrow/include/arrow/util/iterator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/iterator.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/types.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types.h',
   'DATA'),
  ('pyarrow/include/parquet/metadata.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_fixup.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_fixup.h',
   'DATA'),
  ('pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow/include/arrow/json/parser.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/parser.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   'DATA'),
  ('pyarrow/include/arrow/compute/kernel.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/kernel.h',
   'DATA'),
  ('pyarrow/include/parquet/statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/statistics.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset_parquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/buffer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/buffer.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_convert.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/util.h',
   'DATA'),
  ('pyarrow/include/parquet/column_scanner.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_scanner.h',
   'DATA'),
  ('pyarrow/include/arrow/util/launder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/launder.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_binary.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/relation.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/relation.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/opaque.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/opaque.h',
   'DATA'),
  ('pyarrow/__init__.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/__init__.pxd',
   'DATA'),
  ('pyarrow/tests/data/orc/README.md',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/README.md',
   'DATA'),
  ('pyarrow/lib.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.pyx',
   'DATA'),
  ('pyarrow/lib_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib_api.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_parser.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_parser.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib_api.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_run_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_run_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/algorithm.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/algorithm.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/test_in_memory_kms.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/test_in_memory_kms.h',
   'DATA'),
  ('pyarrow/include/arrow/python/udf.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/types.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/types.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.cc',
   'DATA'),
  ('pyarrow/include/arrow/pretty_print.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/pretty_print.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_primitive.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/initialize.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/initialize.h',
   'DATA'),
  ('pyarrow/include/arrow/stl.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/stl.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/include/arrow/util/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/converter.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/delimiting.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/delimiting.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/ordering.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/ordering.h',
   'DATA'),
  ('pyarrow/includes/libarrow_feather.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_feather.pxd',
   'DATA'),
  ('pyarrow/include/parquet/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/python/benchmark.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/parquet/stream_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/stream_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_type_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_type_inline.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_system_key_material_store.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_system_key_material_store.h',
   'DATA'),
  ('pyarrow/include/arrow/python/inference.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/include/parquet/column_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_reader.h',
   'DATA'),
  ('pyarrow/include/parquet/geospatial/statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/geospatial/statistics.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.cc',
   'DATA'),
  ('pyarrow/include/arrow/json/object_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/small_vector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/small_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/array/concatenate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/concatenate.h',
   'DATA'),
  ('pyarrow/src/arrow/python/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/util.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/time.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/time.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.cc',
   'DATA'),
  ('pyarrow/include/arrow/json/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/options.h',
   'DATA'),
  ('pyarrow/includes/libgandiva.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libgandiva.pxd',
   'DATA'),
  ('pyarrow/_fs.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_fs.pxd',
   'DATA'),
  ('pyarrow/include/parquet/bloom_filter_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/bloom_filter_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/converter.h',
   'DATA'),
  ('pyarrow/src/arrow/python/CMakeLists.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/table.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/table.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/util.h',
   'DATA'),
  ('pyarrow/include/parquet/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/platform.h',
   'DATA'),
  ('pyarrow/include/parquet/printer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/printer.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/adapter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/adapter.h',
   'DATA'),
  ('pyarrow/include/arrow/c/abi.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/abi.h',
   'DATA'),
  ('pyarrow/include/parquet/windows_fixup.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/windows_fixup.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_interop.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/parquet/column_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/options.h',
   'DATA'),
  ('pyarrow/src/arrow/python/async.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/async.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/util/span.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/span.h',
   'DATA'),
  ('pyarrow/include/arrow/python/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/parquet/api/schema.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/schema.h',
   'DATA'),
  ('pyarrow/tests/data/orc/decimal.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/decimal.orc',
   'DATA'),
  ('pyarrow/include/arrow/util/float16.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/float16.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/expression.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/expression.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.cc',
   'DATA'),
  ('pyarrow/config.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/config.pxi',
   'DATA'),
  ('pyarrow/include/parquet/exception.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/exception.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_adaptive.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_adaptive.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/task_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/task_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   'DATA'),
  ('pyarrow/include/parquet/stream_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/stream_writer.h',
   'DATA'),
  ('pyarrow/includes/libarrow_substrait.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_json.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_json.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/tensorflow/convert.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/tensorflow/convert.h',
   'DATA'),
  ('pyarrow/include/arrow/python/filesystem.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function_options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function_options.h',
   'DATA'),
  ('pyarrow/includes/libparquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libparquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/api.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_set.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_set.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_base.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_base.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/api.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/type_fwd.h',
   'DATA'),
  ('pyarrow/include/parquet/file_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/file_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/debug.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/debug.h',
   'DATA'),
  ('pyarrow/src/arrow/python/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   'DATA'),
  ('pyarrow/include/arrow/python/io.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/io.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/future.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/future.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/string_util.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/tensor.h',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/python/parquet_encryption.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_interop.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/feather.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/feather.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logging.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/logging.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/math.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/math.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow/_acero.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_acero.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/python_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/partition.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/partition.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/random.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/random.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logger.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/logger.h',
   'DATA'),
  ('pyarrow/_orc.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_orc.pxd',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow/memory.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/memory.pxi',
   'DATA'),
  ('pyarrow/table.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/table.pxi',
   'DATA'),
  ('pyarrow/include/arrow/acero/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/api.h',
   'DATA'),
  ('pyarrow/include/arrow/python/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/io/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/io/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/json/rapidjson_defs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/rapidjson_defs.h',
   'DATA'),
  ('pyarrow/include/arrow/util/secure_string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/secure_string.h',
   'DATA'),
  ('pyarrow/include/arrow/json/chunker.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunker.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/python/iterators.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/include/arrow/io/buffered.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/buffered.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/benchmark_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/benchmark_util.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server.h',
   'DATA'),
  ('pyarrow/include/arrow/python/csv.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/json.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/json.h',
   'DATA'),
  ('pyarrow/include/arrow/config.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/config.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_base.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_base.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/test_common.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/python/datetime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/_json.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_json.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/include/arrow/json/from_string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/from_string.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_material_store.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_material_store.h',
   'DATA'),
  ('pyarrow/src/arrow/python/common.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/parallel.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/parallel.h',
   'DATA'),
  ('pyarrow/_compute.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute.pyx',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function.h',
   'DATA'),
  ('pyarrow/include/parquet/xxhasher.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/xxhasher.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_array_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_array_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/backpressure_handler.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/backpressure_handler.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.test1.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow/ipc.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/ipc.pxi',
   'DATA'),
  ('pyarrow/includes/common.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/common.pxd',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_aggregate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_aggregate.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/options.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_middleware.h',
   'DATA'),
  ('pyarrow/include/parquet/bloom_filter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/bloom_filter.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_encryption_key.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_encryption_key.h',
   'DATA'),
  ('pyarrow/include/arrow/util/formatting.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/formatting.h',
   'DATA'),
  ('pyarrow/include/arrow/array/data.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/data.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/strptime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/strptime.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.h',
   'DATA'),
  ('pyarrow/includes/libarrow_acero.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_acero.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/base64.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/base64.h',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/pandas-shim.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/pandas-shim.pxi',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visitor.h',
   'DATA'),
  ('pyarrow/include/arrow/util/value_parsing.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/value_parsing.h',
   'DATA'),
  ('pyarrow/_feather.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_feather.pyx',
   'DATA'),
  ('pyarrow/include/parquet/encryption/encryption.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/encryption.h',
   'DATA'),
  ('pyarrow/includes/libarrow_flight.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_flight.pxd',
   'DATA'),
  ('pyarrow/include/arrow/stl_allocator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/stl_allocator.h',
   'DATA'),
  ('pyarrow/includes/libarrow.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/benchmark_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/benchmark_util.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_primitive.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_union.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_union.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/api.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_orc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_orc.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_run_end.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_run_end.h',
   'DATA'),
  ('pyarrow/_dataset_orc.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_orc.pyx',
   'DATA'),
  ('pyarrow/tests/data/orc/decimal.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/decimal.jsn.gz',
   'DATA'),
  ('pyarrow/include/arrow/stl_iterator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/stl_iterator.h',
   'DATA'),
  ('pyarrow/benchmark.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/benchmark.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/binary_view_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/binary_view_util.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/uuid.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/uuid.h',
   'DATA'),
  ('pyarrow/include/arrow/c/helpers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/hdfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/hdfs.h',
   'DATA'),
  ('pyarrow/tests/pyarrow_cython_example.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_dict.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_dict.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/visibility.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.cc',
   'DATA'),
  ('pyarrow/_dataset.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset.pyx',
   'DATA'),
  ('pyarrow/include/arrow/flight/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz_private.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz_private.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_reader.h',
   'DATA'),
  ('pyarrow/_substrait.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_substrait.pyx',
   'DATA'),
  ('pyarrow/include/arrow/array/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/concurrent_map.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/concurrent_map.h',
   'DATA'),
  ('pyarrow/include/arrow/util/key_value_metadata.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/key_value_metadata.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/_s3fs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_s3fs.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.cc',
   'DATA'),
  ('pyarrow/include/arrow/io/mman.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/mman.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.cc',
   'DATA'),
  ('pyarrow/include/arrow/csv/parser.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/parser.h',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('certifi/cacert.pem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-themes.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-themes.json',
   'DATA'),
  ('altair/jupyter/js/index.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/js/index.js',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-lite-schema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-lite-schema.json',
   'DATA'),
  ('altair/jupyter/js/README.md',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/js/README.md',
   'DATA'),
  ('altair/py.typed',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/py.typed',
   'DATA'),
  ('jsonschema_specifications/schemas/draft3/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft3/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/content',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft7/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft7/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft4/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft4/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft6/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft6/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/content',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/WHEEL',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/RECORD',
   'DATA'),
  ('jsonschema/benchmarks/issue232/issue.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/benchmarks/issue232/issue.json',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/INSTALLER',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/entry_points.txt',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/METADATA',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/licenses/COPYING',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/licenses/COPYING',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info/licenses/LICENSE',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/base_library.zip',
   'DATA'),
  ('libarrow_substrait.2100.dylib',
   'pyarrow/libarrow_substrait.2100.dylib',
   'SYMLINK'),
  ('libparquet.2100.dylib', 'pyarrow/libparquet.2100.dylib', 'SYMLINK'),
  ('libarrow.2100.dylib', 'pyarrow/libarrow.2100.dylib', 'SYMLINK'),
  ('libarrow_acero.2100.dylib', 'pyarrow/libarrow_acero.2100.dylib', 'SYMLINK'),
  ('libarrow_compute.2100.dylib',
   'pyarrow/libarrow_compute.2100.dylib',
   'SYMLINK'),
  ('libarrow_dataset.2100.dylib',
   'pyarrow/libarrow_dataset.2100.dylib',
   'SYMLINK'),
  ('libarrow_python.2100.dylib',
   'pyarrow/libarrow_python.2100.dylib',
   'SYMLINK'),
  ('libarrow_flight.2100.dylib',
   'pyarrow/libarrow_flight.2100.dylib',
   'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libarrow_python_parquet_encryption.2100.dylib',
   'pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   'SYMLINK'),
  ('libarrow_python_flight.2100.dylib',
   'pyarrow/libarrow_python_flight.2100.dylib',
   'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('libavif.16.3.0.dylib', 'PIL/.dylibs/libavif.16.3.0.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK')],
 [],
 False,
 False,
 1754019897,
 [('run',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/bootloader/Darwin-64bit/run',
   'EXECUTABLE')],
 '/opt/miniconda3/envs/dist/lib/libpython3.13.dylib')
