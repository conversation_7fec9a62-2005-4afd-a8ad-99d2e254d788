(['/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/run.py'],
 ['/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist'],
 ['streamlit',
  'streamlit.web.cli',
  'streamlit.runtime.scriptrunner.script_runner',
  'streamlit.runtime.state',
  'streamlit.runtime.caching',
  'streamlit.runtime.uploaded_file_manager',
  'streamlit.components.v1.components',
  'streamlit.delta_generator',
  'streamlit.elements',
  'streamlit.elements.form',
  'streamlit.elements.widgets',
  'streamlit.elements.lib',
  'streamlit.elements.lib.column_config_utils',
  'streamlit.elements.lib.policies',
  'streamlit.proto',
  'streamlit.runtime.metrics_util',
  'streamlit.runtime.legacy_caching',
  'streamlit.runtime.forward_msg_queue',
  'streamlit.runtime.app_session',
  'streamlit.runtime.runtime',
  'streamlit.runtime.memory',
  'streamlit.runtime.stats',
  'streamlit.web.server',
  'streamlit.web.server.server',
  'streamlit.web.server.routes',
  'streamlit.web.server.media_file_handler',
  'streamlit.web.server.component_request_handler',
  'streamlit.web.server.upload_file_request_handler',
  'streamlit.web.server.health_handler',
  'streamlit.web.server.message_cache_handler',
  'streamlit.web.server.stats_request_handler',
  'streamlit.logger',
  'streamlit.config',
  'streamlit.file_util',
  'streamlit.source_util',
  'streamlit.string_util',
  'streamlit.type_util',
  'streamlit.util',
  'streamlit.watcher',
  'streamlit.folder_black_list',
  'streamlit.git_util',
  'streamlit.env_util',
  'streamlit.bootstrap',
  'streamlit.case_converters',
  'streamlit.cursor',
  'streamlit.deprecation_util',
  'streamlit.error_util',
  'streamlit.hash_util',
  'streamlit.in_memory_file_manager',
  'streamlit.report_thread',
  'streamlit.server.server_util',
  'streamlit.state.session_state_proxy',
  'streamlit.state.widgets',
  'streamlit.uploaded_file_manager',
  'streamlit.user_info',
  'streamlit.version',
  'streamlit.web.bootstrap',
  'pandas',
  'numpy',
  'altair',
  'plotly',
  'tornado',
  'tornado.web',
  'tornado.websocket',
  'tornado.ioloop',
  'tornado.httpserver',
  'tornado.netutil',
  'tornado.options',
  'tornado.log',
  'tornado.escape',
  'tornado.util',
  'tornado.concurrent',
  'tornado.gen',
  'tornado.locks',
  'tornado.queues',
  'tornado.iostream',
  'tornado.tcpserver',
  'tornado.process',
  'tornado.platform',
  'tornado.platform.auto',
  'tornado.routing',
  'tornado.auth',
  'tornado.locale',
  'tornado.template',
  'tornado.testing',
  'tornado.httpclient',
  'tornado.httputil',
  'tornado.simple_httpclient',
  'tornado.curl_httpclient',
  'tornado.wsgi',
  'click',
  'click.core',
  'click.decorators',
  'click.exceptions',
  'click.formatting',
  'click.options',
  'click.parser',
  'click.types',
  'click.utils',
  'toml',
  'validators',
  'packaging',
  'packaging.version',
  'packaging.specifiers',
  'packaging.requirements',
  'pyarrow',
  'pyarrow.lib',
  'pyarrow.parquet',
  'pyarrow.csv',
  'pyarrow.json',
  'pyarrow.feather',
  'pyarrow.orc',
  'pyarrow.dataset',
  'pyarrow.compute',
  'pyarrow.types',
  'pyarrow.schema',
  'pyarrow.table',
  'pyarrow.array',
  'pyarrow.scalar',
  'pyarrow.tensor',
  'pyarrow.plasma',
  'pyarrow.flight',
  'pyarrow.gandiva',
  'pyarrow.cuda',
  'pyarrow.fs',
  'pyarrow.hdfs',
  'pyarrow.s3fs',
  'pyarrow.gcsfs',
  'pyarrow.azurefs',
  'pyarrow.substrait',
  'pyarrow.acero',
  'pyarrow.interchange',
  'pyarrow.ipc',
  'pyarrow.memory',
  'pyarrow.util',
  'pyarrow.vendored',
  'pyarrow.vendored.version',
  'pyarrow._compute',
  'pyarrow._csv',
  'pyarrow._dataset',
  'pyarrow._feather',
  'pyarrow._flight',
  'pyarrow._fs',
  'pyarrow._gandiva',
  'pyarrow._hdfs',
  'pyarrow._json',
  'pyarrow._orc',
  'pyarrow._parquet',
  'pyarrow._plasma',
  'pyarrow._s3fs',
  'pyarrow._substrait',
  'pyarrow.lib',
  'blinker',
  'blinker.base',
  'blinker._utilities',
  'blinker._saferef',
  'protobuf',
  'google.protobuf',
  'google.protobuf.message',
  'google.protobuf.descriptor',
  'google.protobuf.descriptor_pb2',
  'google.protobuf.message_factory',
  'google.protobuf.reflection',
  'google.protobuf.service',
  'google.protobuf.service_reflection',
  'google.protobuf.text_format',
  'google.protobuf.json_format',
  'google.protobuf.util',
  'google.protobuf.util.json_format',
  'google.protobuf.util.message_util',
  'google.protobuf.util.type_util',
  'google.protobuf.compiler',
  'google.protobuf.compiler.plugin_pb2',
  'google.protobuf.internal',
  'google.protobuf.internal.api_implementation',
  'google.protobuf.internal.containers',
  'google.protobuf.internal.decoder',
  'google.protobuf.internal.encoder',
  'google.protobuf.internal.enum_type_wrapper',
  'google.protobuf.internal.extension_dict',
  'google.protobuf.internal.message_listener',
  'google.protobuf.internal.python_message',
  'google.protobuf.internal.type_checkers',
  'google.protobuf.internal.well_known_types',
  'google.protobuf.internal.wire_format',
  'google.protobuf.pyext',
  'google.protobuf.pyext._message',
  'google.protobuf.symbol_database',
  'google.protobuf.descriptor_database',
  'google.protobuf.descriptor_pool',
  'google.protobuf.message_factory',
  'google.protobuf.proto_builder',
  'google.protobuf.text_encoding',
  'google.protobuf.unknown_fields',
  'google.protobuf.wrappers_pb2',
  'google.protobuf.any_pb2',
  'google.protobuf.api_pb2',
  'google.protobuf.duration_pb2',
  'google.protobuf.empty_pb2',
  'google.protobuf.field_mask_pb2',
  'google.protobuf.source_context_pb2',
  'google.protobuf.struct_pb2',
  'google.protobuf.timestamp_pb2',
  'google.protobuf.type_pb2'],
 [('./hooks', 1000),
  ('/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_pyinstaller',
   0),
  ('/opt/miniconda3/envs/dist/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/opt/miniconda3/envs/dist/lib/python3.13/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('ui.py',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/ui.py',
   'DATA')],
 '3.13.5 | packaged by conda-forge | (main, Jun 16 2025, 08:24:05) [Clang '
 '18.1.8 ]',
 [('pyi_rth_inspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('run',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/run.py',
   'PYSOURCE')],
 [('subprocess',
   '/opt/miniconda3/envs/dist/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('contextlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('threading',
   '/opt/miniconda3/envs/dist/lib/python3.13/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/opt/miniconda3/envs/dist/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('signal', '/opt/miniconda3/envs/dist/lib/python3.13/signal.py', 'PYMODULE'),
  ('_strptime',
   '/opt/miniconda3/envs/dist/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('datetime',
   '/opt/miniconda3/envs/dist/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('calendar',
   '/opt/miniconda3/envs/dist/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('shutil', '/opt/miniconda3/envs/dist/lib/python3.13/shutil.py', 'PYMODULE'),
  ('zipfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('pathlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   '/opt/miniconda3/envs/dist/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('urllib.parse',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('ipaddress',
   '/opt/miniconda3/envs/dist/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('glob', '/opt/miniconda3/envs/dist/lib/python3.13/glob.py', 'PYMODULE'),
  ('pathlib._abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('py_compile',
   '/opt/miniconda3/envs/dist/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('csv', '/opt/miniconda3/envs/dist/lib/python3.13/csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('string', '/opt/miniconda3/envs/dist/lib/python3.13/string.py', 'PYMODULE'),
  ('email.headerregistry',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('random', '/opt/miniconda3/envs/dist/lib/python3.13/random.py', 'PYMODULE'),
  ('statistics',
   '/opt/miniconda3/envs/dist/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/opt/miniconda3/envs/dist/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/opt/miniconda3/envs/dist/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/opt/miniconda3/envs/dist/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/opt/miniconda3/envs/dist/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/opt/miniconda3/envs/dist/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('pickle', '/opt/miniconda3/envs/dist/lib/python3.13/pickle.py', 'PYMODULE'),
  ('pprint', '/opt/miniconda3/envs/dist/lib/python3.13/pprint.py', 'PYMODULE'),
  ('dataclasses',
   '/opt/miniconda3/envs/dist/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/opt/miniconda3/envs/dist/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('bisect', '/opt/miniconda3/envs/dist/lib/python3.13/bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('base64', '/opt/miniconda3/envs/dist/lib/python3.13/base64.py', 'PYMODULE'),
  ('getopt', '/opt/miniconda3/envs/dist/lib/python3.13/getopt.py', 'PYMODULE'),
  ('email.charset',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('socket', '/opt/miniconda3/envs/dist/lib/python3.13/socket.py', 'PYMODULE'),
  ('email._parseaddr',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('quopri', '/opt/miniconda3/envs/dist/lib/python3.13/quopri.py', 'PYMODULE'),
  ('typing', '/opt/miniconda3/envs/dist/lib/python3.13/typing.py', 'PYMODULE'),
  ('importlib.abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('importlib._abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('inspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('token', '/opt/miniconda3/envs/dist/lib/python3.13/token.py', 'PYMODULE'),
  ('dis', '/opt/miniconda3/envs/dist/lib/python3.13/dis.py', 'PYMODULE'),
  ('opcode', '/opt/miniconda3/envs/dist/lib/python3.13/opcode.py', 'PYMODULE'),
  ('_opcode_metadata',
   '/opt/miniconda3/envs/dist/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('ast', '/opt/miniconda3/envs/dist/lib/python3.13/ast.py', 'PYMODULE'),
  ('email',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('json',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('__future__',
   '/opt/miniconda3/envs/dist/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/opt/miniconda3/envs/dist/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('struct', '/opt/miniconda3/envs/dist/lib/python3.13/struct.py', 'PYMODULE'),
  ('importlib.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('gzip', '/opt/miniconda3/envs/dist/lib/python3.13/gzip.py', 'PYMODULE'),
  ('_compression',
   '/opt/miniconda3/envs/dist/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('lzma', '/opt/miniconda3/envs/dist/lib/python3.13/lzma.py', 'PYMODULE'),
  ('bz2', '/opt/miniconda3/envs/dist/lib/python3.13/bz2.py', 'PYMODULE'),
  ('fnmatch',
   '/opt/miniconda3/envs/dist/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('copy', '/opt/miniconda3/envs/dist/lib/python3.13/copy.py', 'PYMODULE'),
  ('gettext',
   '/opt/miniconda3/envs/dist/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/opt/miniconda3/envs/dist/lib/python3.13/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/opt/miniconda3/envs/dist/lib/python3.13/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/opt/miniconda3/envs/dist/lib/python3.13/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/opt/miniconda3/envs/dist/lib/python3.13/nturl2path.py',
   'PYMODULE'),
  ('ftplib', '/opt/miniconda3/envs/dist/lib/python3.13/ftplib.py', 'PYMODULE'),
  ('netrc', '/opt/miniconda3/envs/dist/lib/python3.13/netrc.py', 'PYMODULE'),
  ('mimetypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/__init__.py',
   'PYMODULE'),
  ('ssl', '/opt/miniconda3/envs/dist/lib/python3.13/ssl.py', 'PYMODULE'),
  ('urllib.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/error.py',
   'PYMODULE'),
  ('xml.sax',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('http.client',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/client.py',
   'PYMODULE'),
  ('hmac', '/opt/miniconda3/envs/dist/lib/python3.13/hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue', '/opt/miniconda3/envs/dist/lib/python3.13/queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/opt/miniconda3/envs/dist/lib/python3.13/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy', '/opt/miniconda3/envs/dist/lib/python3.13/runpy.py', 'PYMODULE'),
  ('pkgutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('google.protobuf.type_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/type_pb2.py',
   'PYMODULE'),
  ('google.protobuf.internal.builder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/builder.py',
   'PYMODULE'),
  ('google.protobuf.runtime_version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/runtime_version.py',
   'PYMODULE'),
  ('google.protobuf.timestamp_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/timestamp_pb2.py',
   'PYMODULE'),
  ('google.protobuf.struct_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/struct_pb2.py',
   'PYMODULE'),
  ('google.protobuf.source_context_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/source_context_pb2.py',
   'PYMODULE'),
  ('google.protobuf.field_mask_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/field_mask_pb2.py',
   'PYMODULE'),
  ('google.protobuf.empty_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/empty_pb2.py',
   'PYMODULE'),
  ('google.protobuf.duration_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/duration_pb2.py',
   'PYMODULE'),
  ('google.protobuf.api_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/api_pb2.py',
   'PYMODULE'),
  ('google.protobuf.any_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/any_pb2.py',
   'PYMODULE'),
  ('google.protobuf.wrappers_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/wrappers_pb2.py',
   'PYMODULE'),
  ('google.protobuf.unknown_fields',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/unknown_fields.py',
   'PYMODULE'),
  ('google.protobuf.text_encoding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/text_encoding.py',
   'PYMODULE'),
  ('google.protobuf.proto_builder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/proto_builder.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pool',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor_pool.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_edition_defaults',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/python_edition_defaults.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_database',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor_database.py',
   'PYMODULE'),
  ('google.protobuf.symbol_database',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/symbol_database.py',
   'PYMODULE'),
  ('google.protobuf.pyext',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/pyext/__init__.py',
   'PYMODULE'),
  ('google.protobuf.pyext.cpp_message',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/pyext/cpp_message.py',
   'PYMODULE'),
  ('google.protobuf.internal.wire_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/wire_format.py',
   'PYMODULE'),
  ('google.protobuf.internal.well_known_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/well_known_types.py',
   'PYMODULE'),
  ('google.protobuf.internal.field_mask',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/field_mask.py',
   'PYMODULE'),
  ('google.protobuf.internal.type_checkers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/type_checkers.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_message',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/python_message.py',
   'PYMODULE'),
  ('google.protobuf.internal.message_listener',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/message_listener.py',
   'PYMODULE'),
  ('google.protobuf.internal.extension_dict',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/extension_dict.py',
   'PYMODULE'),
  ('google.protobuf.internal.enum_type_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/enum_type_wrapper.py',
   'PYMODULE'),
  ('google.protobuf.internal.encoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/encoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.decoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/decoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.containers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/containers.py',
   'PYMODULE'),
  ('google.protobuf.internal.api_implementation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/api_implementation.py',
   'PYMODULE'),
  ('google._upb', '-', 'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google.protobuf.internal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/__init__.py',
   'PYMODULE'),
  ('google.protobuf.compiler.plugin_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/compiler/plugin_pb2.py',
   'PYMODULE'),
  ('google.protobuf.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/compiler/__init__.py',
   'PYMODULE'),
  ('google.protobuf.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/util/__init__.py',
   'PYMODULE'),
  ('google.protobuf.json_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/json_format.py',
   'PYMODULE'),
  ('google.protobuf.text_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/text_format.py',
   'PYMODULE'),
  ('google.protobuf.service_reflection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/service_reflection.py',
   'PYMODULE'),
  ('google.protobuf.reflection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/reflection.py',
   'PYMODULE'),
  ('google.protobuf.message_factory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/message_factory.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor.py',
   'PYMODULE'),
  ('google.protobuf.message',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/message.py',
   'PYMODULE'),
  ('google.protobuf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/blinker/_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/blinker/base.py',
   'PYMODULE'),
  ('blinker',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/blinker/__init__.py',
   'PYMODULE'),
  ('pyarrow.vendored.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/vendored/version.py',
   'PYMODULE'),
  ('pyarrow.vendored',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/vendored/__init__.py',
   'PYMODULE'),
  ('pyarrow.vendored.docscrape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/vendored/docscrape.py',
   'PYMODULE'),
  ('pydoc', '/opt/miniconda3/envs/dist/lib/python3.13/pydoc.py', 'PYMODULE'),
  ('webbrowser',
   '/opt/miniconda3/envs/dist/lib/python3.13/webbrowser.py',
   'PYMODULE'),
  ('_ios_support',
   '/opt/miniconda3/envs/dist/lib/python3.13/_ios_support.py',
   'PYMODULE'),
  ('shlex', '/opt/miniconda3/envs/dist/lib/python3.13/shlex.py', 'PYMODULE'),
  ('http.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/socketserver.py',
   'PYMODULE'),
  ('html',
   '/opt/miniconda3/envs/dist/lib/python3.13/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/opt/miniconda3/envs/dist/lib/python3.13/html/entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/opt/miniconda3/envs/dist/lib/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/pydoc_data/__init__.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pyrepl/pager.py',
   'PYMODULE'),
  ('_pyrepl',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pyrepl/__init__.py',
   'PYMODULE'),
  ('tty', '/opt/miniconda3/envs/dist/lib/python3.13/tty.py', 'PYMODULE'),
  ('sysconfig',
   '/opt/miniconda3/envs/dist/lib/python3.13/sysconfig/__init__.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/opt/miniconda3/envs/dist/lib/python3.13/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_aix_support',
   '/opt/miniconda3/envs/dist/lib/python3.13/_aix_support.py',
   'PYMODULE'),
  ('_osx_support',
   '/opt/miniconda3/envs/dist/lib/python3.13/_osx_support.py',
   'PYMODULE'),
  ('platform',
   '/opt/miniconda3/envs/dist/lib/python3.13/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/plistlib.py',
   'PYMODULE'),
  ('pyarrow.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/util.py',
   'PYMODULE'),
  ('requests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/compat.py',
   'PYMODULE'),
  ('http.cookies',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/cookies.py',
   'PYMODULE'),
  ('requests.models',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/models.py',
   'PYMODULE'),
  ('idna',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.auth',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('requests.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.sessions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/utils.py',
   'PYMODULE'),
  ('requests.certs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/certs.py',
   'PYMODULE'),
  ('certifi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/core.py',
   'PYMODULE'),
  ('requests.packages',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('urllib3',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('uuid', '/opt/miniconda3/envs/dist/lib/python3.13/uuid.py', 'PYMODULE'),
  ('pyarrow.ipc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/ipc.py',
   'PYMODULE'),
  ('pyarrow.interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/__init__.py',
   'PYMODULE'),
  ('pyarrow.interchange.from_dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pyarrow.interchange.column',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/column.py',
   'PYMODULE'),
  ('pyarrow.interchange.buffer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/buffer.py',
   'PYMODULE'),
  ('pyarrow.acero',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/acero.py',
   'PYMODULE'),
  ('pyarrow.substrait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/substrait.py',
   'PYMODULE'),
  ('pyarrow.fs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/fs.py',
   'PYMODULE'),
  ('pyarrow.cuda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/cuda.py',
   'PYMODULE'),
  ('pyarrow.flight',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/flight.py',
   'PYMODULE'),
  ('pyarrow.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/types.py',
   'PYMODULE'),
  ('pyarrow.compute',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/compute.py',
   'PYMODULE'),
  ('pyarrow._compute_docstrings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute_docstrings.py',
   'PYMODULE'),
  ('pyarrow.dataset',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/dataset.py',
   'PYMODULE'),
  ('pyarrow.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/orc.py',
   'PYMODULE'),
  ('pyarrow.feather',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/feather.py',
   'PYMODULE'),
  ('pyarrow.pandas_compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/pandas_compat.py',
   'PYMODULE'),
  ('pandas.core.internals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/blocks.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_mixins.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/util/hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/sorting.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/ops.py',
   'PYMODULE'),
  ('pandas.core.generic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/generic.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/datetimes.py',
   'PYMODULE'),
  ('pandas.arrays',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/style.py',
   'PYMODULE'),
  ('jinja2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/markupsafe/__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/markupsafe/_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/nodes.py',
   'PYMODULE'),
  ('pandas.io.formats',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/types/__init__.py',
   'PYMODULE'),
  ('pandas.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/typing/__init__.py',
   'PYMODULE'),
  ('pandas.io.stata',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/stata.py',
   'PYMODULE'),
  ('pandas.io.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/base.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/_json.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/readers.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/base_parser.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/masked.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_utils.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/_normalize.py',
   'PYMODULE'),
  ('pandas.io._util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/_util.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/extensions/__init__.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/api.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/clipboard/__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/wintypes.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sql.py',
   'PYMODULE'),
  ('sqlite3',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/__main__.py',
   'PYMODULE'),
  ('code', '/opt/miniconda3/envs/dist/lib/python3.13/code.py', 'PYMODULE'),
  ('codeop', '/opt/miniconda3/envs/dist/lib/python3.13/codeop.py', 'PYMODULE'),
  ('sqlite3.dbapi2',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/common.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/scope.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/eval.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/check.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/align.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/expr.py',
   'PYMODULE'),
  ('pandas.io.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.io',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/__init__.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/gbq.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_calamine.py',
   'PYMODULE'),
  ('pandas.util.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/version/__init__.py',
   'PYMODULE'),
  ('pandas.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/_color_data.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/parsing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/resample.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/offsets.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/mean_.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/util/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/string_arrow.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/object_array.py',
   'PYMODULE'),
  ('pandas.core.strings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/merge.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/frozen.py',
   'PYMODULE'),
  ('pandas.plotting',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/plotting/_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/plotting/_core.py',
   'PYMODULE'),
  ('pandas.core.apply',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/apply.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/extensions.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/common.py',
   'PYMODULE'),
  ('pandas._libs.window',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexers/objects.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/printing.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/console.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/html.py',
   'PYMODULE'),
  ('pandas.core.window',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/online.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/shared_docs.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/categorical.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/describe.py',
   'PYMODULE'),
  ('pandas.core.methods',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/selectn.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/construction.py',
   'PYMODULE'),
  ('numpy.ma',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('doctest',
   '/opt/miniconda3/envs/dist/lib/python3.13/doctest.py',
   'PYMODULE'),
  ('_colorize',
   '/opt/miniconda3/envs/dist/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('pdb', '/opt/miniconda3/envs/dist/lib/python3.13/pdb.py', 'PYMODULE'),
  ('rlcompleter',
   '/opt/miniconda3/envs/dist/lib/python3.13/rlcompleter.py',
   'PYMODULE'),
  ('bdb', '/opt/miniconda3/envs/dist/lib/python3.13/bdb.py', 'PYMODULE'),
  ('cmd', '/opt/miniconda3/envs/dist/lib/python3.13/cmd.py', 'PYMODULE'),
  ('difflib',
   '/opt/miniconda3/envs/dist/lib/python3.13/difflib.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/printoptions.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._globals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/version.py',
   'PYMODULE'),
  ('unittest.case',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/_log.py',
   'PYMODULE'),
  ('unittest.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/util.py',
   'PYMODULE'),
  ('unittest.result',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/result.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('unittest',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/suite.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('pandas.core.flags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/flags.py',
   'PYMODULE'),
  ('pandas.core.sample',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexing.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/_optional.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/_constants.py',
   'PYMODULE'),
  ('pandas.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/pyarrow.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/compressors.py',
   'PYMODULE'),
  ('pandas._config.config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/config.py',
   'PYMODULE'),
  ('pandas.core.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/accessor.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/roperator.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.core.frame',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/util.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parquet.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'PYMODULE'),
  ('pickletools',
   '/opt/miniconda3/envs/dist/lib/python3.13/pickletools.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/interval.py',
   'PYMODULE'),
  ('numpy.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/feather_format.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/column.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/buffer.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/melt.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/take.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/period.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_ranges.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/frequencies.py',
   'PYMODULE'),
  ('pandas.tseries',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/common.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/array.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/times.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arraylike.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/nanops.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/concat.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/numpy/function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/numpy/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/range.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/period.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('six',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/six.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil._common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('pytz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/category.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/base.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas.core.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/numpy_.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexers/utils.py',
   'PYMODULE'),
  ('pandas.core.construction',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/construction.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/expressions.py',
   'PYMODULE'),
  ('pandas.core.computation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/__init__.py',
   'PYMODULE'),
  ('pandas.core.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/common.py',
   'PYMODULE'),
  ('pandas.core.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/string_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/putmask.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/algorithms.py',
   'PYMODULE'),
  ('pandas.core.missing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/astype.py',
   'PYMODULE'),
  ('pandas.util._validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_validators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_exceptions.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_decorators.py',
   'PYMODULE'),
  ('pandas.errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/errors/__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_typing.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/holiday.py',
   'PYMODULE'),
  ('pandas._libs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/__init__.py',
   'PYMODULE'),
  ('pandas._config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/dates.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/ops.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/concat.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/api.py',
   'PYMODULE'),
  ('pandas.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/__init__.py',
   'PYMODULE'),
  ('pyarrow.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/json.py',
   'PYMODULE'),
  ('pyarrow.csv',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/csv.py',
   'PYMODULE'),
  ('pyarrow.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/parquet/__init__.py',
   'PYMODULE'),
  ('pyarrow.parquet.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/parquet/core.py',
   'PYMODULE'),
  ('pyarrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/__init__.py',
   'PYMODULE'),
  ('pyarrow.parquet.encryption',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/parquet/encryption.py',
   'PYMODULE'),
  ('pyarrow.jvm',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/jvm.py',
   'PYMODULE'),
  ('pyarrow.interchange.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/dataframe.py',
   'PYMODULE'),
  ('pyarrow.conftest',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/conftest.py',
   'PYMODULE'),
  ('pyarrow.tests.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/util.py',
   'PYMODULE'),
  ('pyarrow.tests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('pyarrow.cffi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/cffi.py',
   'PYMODULE'),
  ('pyarrow.benchmark',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/benchmark.py',
   'PYMODULE'),
  ('pyarrow._generated_version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_generated_version.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.markers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging._structures',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('toml',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/__init__.py',
   'PYMODULE'),
  ('toml.decoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/decoder.py',
   'PYMODULE'),
  ('toml.tz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/tz.py',
   'PYMODULE'),
  ('toml.encoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/encoder.py',
   'PYMODULE'),
  ('click.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/utils.py',
   'PYMODULE'),
  ('click.globals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/globals.py',
   'PYMODULE'),
  ('click._compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_winconsole.py',
   'PYMODULE'),
  ('click.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/types.py',
   'PYMODULE'),
  ('click.shell_completion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/shell_completion.py',
   'PYMODULE'),
  ('click.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/parser.py',
   'PYMODULE'),
  ('click.formatting',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/decorators.py',
   'PYMODULE'),
  ('click.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/core.py',
   'PYMODULE'),
  ('click.termui',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_termui_impl.py',
   'PYMODULE'),
  ('click',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/__init__.py',
   'PYMODULE'),
  ('tornado.wsgi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/wsgi.py',
   'PYMODULE'),
  ('tornado.curl_httpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/curl_httpclient.py',
   'PYMODULE'),
  ('tornado.simple_httpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/simple_httpclient.py',
   'PYMODULE'),
  ('tornado.tcpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/tcpclient.py',
   'PYMODULE'),
  ('tornado.http1connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/http1connection.py',
   'PYMODULE'),
  ('tornado.httputil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/httputil.py',
   'PYMODULE'),
  ('tornado.httpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/httpclient.py',
   'PYMODULE'),
  ('tornado.testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/testing.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/platform/asyncio.py',
   'PYMODULE'),
  ('tornado.template',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/template.py',
   'PYMODULE'),
  ('tornado.locale',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/locale.py',
   'PYMODULE'),
  ('tornado._locale_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/_locale_data.py',
   'PYMODULE'),
  ('tornado.auth',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/auth.py',
   'PYMODULE'),
  ('tornado.routing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/routing.py',
   'PYMODULE'),
  ('tornado.platform',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/platform/__init__.py',
   'PYMODULE'),
  ('tornado.process',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/process.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/tcpserver.py',
   'PYMODULE'),
  ('tornado.iostream',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/iostream.py',
   'PYMODULE'),
  ('tornado.queues',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/queues.py',
   'PYMODULE'),
  ('tornado.locks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/locks.py',
   'PYMODULE'),
  ('tornado.gen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/gen.py',
   'PYMODULE'),
  ('tornado.concurrent',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/concurrent.py',
   'PYMODULE'),
  ('tornado.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/util.py',
   'PYMODULE'),
  ('tornado.escape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/escape.py',
   'PYMODULE'),
  ('tornado.log',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/log.py',
   'PYMODULE'),
  ('curses',
   '/opt/miniconda3/envs/dist/lib/python3.13/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/opt/miniconda3/envs/dist/lib/python3.13/curses/has_key.py',
   'PYMODULE'),
  ('logging.handlers',
   '/opt/miniconda3/envs/dist/lib/python3.13/logging/handlers.py',
   'PYMODULE'),
  ('smtplib',
   '/opt/miniconda3/envs/dist/lib/python3.13/smtplib.py',
   'PYMODULE'),
  ('tornado.options',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/options.py',
   'PYMODULE'),
  ('tornado.netutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/netutil.py',
   'PYMODULE'),
  ('tornado.httpserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/httpserver.py',
   'PYMODULE'),
  ('tornado.ioloop',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/ioloop.py',
   'PYMODULE'),
  ('tornado.websocket',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/websocket.py',
   'PYMODULE'),
  ('tornado.web',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/web.py',
   'PYMODULE'),
  ('tornado.autoreload',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/autoreload.py',
   'PYMODULE'),
  ('optparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/optparse.py',
   'PYMODULE'),
  ('tornado',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/__init__.py',
   'PYMODULE'),
  ('altair',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/__init__.py',
   'PYMODULE'),
  ('altair.utils.deprecation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/deprecation.py',
   'PYMODULE'),
  ('altair._magics',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/_magics.py',
   'PYMODULE'),
  ('altair.vegalite.v5',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/__init__.py',
   'PYMODULE'),
  ('altair.vegalite.v5.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/display.py',
   'PYMODULE'),
  ('altair.utils._show',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_show.py',
   'PYMODULE'),
  ('altair.vegalite.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/display.py',
   'PYMODULE'),
  ('altair.utils.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/display.py',
   'PYMODULE'),
  ('altair.utils.schemapi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/schemapi.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/typing.py',
   'PYMODULE'),
  ('referencing._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/_core.py',
   'PYMODULE'),
  ('rpds',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/rpds/__init__.py',
   'PYMODULE'),
  ('attrs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/__init__.py',
   'PYMODULE'),
  ('attrs.validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/validators.py',
   'PYMODULE'),
  ('attr.validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/validators.py',
   'PYMODULE'),
  ('attr.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/converters.py',
   'PYMODULE'),
  ('attr._compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_compat.py',
   'PYMODULE'),
  ('attr._make',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_make.py',
   'PYMODULE'),
  ('attr.setters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/setters.py',
   'PYMODULE'),
  ('attr._config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_config.py',
   'PYMODULE'),
  ('attrs.setters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/setters.py',
   'PYMODULE'),
  ('attrs.filters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/filters.py',
   'PYMODULE'),
  ('attr.filters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/filters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/exceptions.py',
   'PYMODULE'),
  ('attrs.converters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/converters.py',
   'PYMODULE'),
  ('attr._next_gen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_next_gen.py',
   'PYMODULE'),
  ('attr._funcs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_funcs.py',
   'PYMODULE'),
  ('attr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/__init__.py',
   'PYMODULE'),
  ('attr._version_info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_version_info.py',
   'PYMODULE'),
  ('attr._cmp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_cmp.py',
   'PYMODULE'),
  ('referencing._attrs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/_attrs.py',
   'PYMODULE'),
  ('referencing.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/exceptions.py',
   'PYMODULE'),
  ('referencing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/__init__.py',
   'PYMODULE'),
  ('narwhals.stable.v1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/__init__.py',
   'PYMODULE'),
  ('narwhals.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/dtypes.py',
   'PYMODULE'),
  ('narwhals._translate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_translate.py',
   'PYMODULE'),
  ('narwhals.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/typing.py',
   'PYMODULE'),
  ('narwhals._compliant',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/__init__.py',
   'PYMODULE'),
  ('narwhals._compliant.window',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/window.py',
   'PYMODULE'),
  ('narwhals._compliant.when_then',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/when_then.py',
   'PYMODULE'),
  ('narwhals._compliant.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/typing.py',
   'PYMODULE'),
  ('narwhals._compliant.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/series.py',
   'PYMODULE'),
  ('narwhals._compliant.any_namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/any_namespace.py',
   'PYMODULE'),
  ('narwhals._compliant.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/selectors.py',
   'PYMODULE'),
  ('narwhals._compliant.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/namespace.py',
   'PYMODULE'),
  ('narwhals._compliant.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/group_by.py',
   'PYMODULE'),
  ('narwhals._compliant.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/expr.py',
   'PYMODULE'),
  ('narwhals._expression_parsing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_expression_parsing.py',
   'PYMODULE'),
  ('narwhals._compliant.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/dataframe.py',
   'PYMODULE'),
  ('narwhals.translate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/translate.py',
   'PYMODULE'),
  ('narwhals._interchange.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_interchange/dataframe.py',
   'PYMODULE'),
  ('narwhals._interchange.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_interchange/series.py',
   'PYMODULE'),
  ('narwhals._interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_interchange/__init__.py',
   'PYMODULE'),
  ('narwhals._namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_namespace.py',
   'PYMODULE'),
  ('narwhals._spark_like.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/namespace.py',
   'PYMODULE'),
  ('narwhals._sql.when_then',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/when_then.py',
   'PYMODULE'),
  ('narwhals._sql',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/__init__.py',
   'PYMODULE'),
  ('narwhals._sql.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/typing.py',
   'PYMODULE'),
  ('narwhals._sql.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/expr.py',
   'PYMODULE'),
  ('narwhals._sql.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/dataframe.py',
   'PYMODULE'),
  ('narwhals._sql.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/namespace.py',
   'PYMODULE'),
  ('narwhals._spark_like.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/utils.py',
   'PYMODULE'),
  ('narwhals._spark_like.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/selectors.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_struct.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_str.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_list.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_dt.py',
   'PYMODULE'),
  ('narwhals._duration',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duration.py',
   'PYMODULE'),
  ('narwhals._spark_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/__init__.py',
   'PYMODULE'),
  ('narwhals._spark_like.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/dataframe.py',
   'PYMODULE'),
  ('narwhals._polars.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/dataframe.py',
   'PYMODULE'),
  ('narwhals._ibis.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/dataframe.py',
   'PYMODULE'),
  ('narwhals._ibis.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/series.py',
   'PYMODULE'),
  ('narwhals._ibis.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/group_by.py',
   'PYMODULE'),
  ('narwhals._sql.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/group_by.py',
   'PYMODULE'),
  ('narwhals._ibis.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_struct.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_str.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_list.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_dt.py',
   'PYMODULE'),
  ('narwhals._ibis.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/utils.py',
   'PYMODULE'),
  ('narwhals._ibis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/__init__.py',
   'PYMODULE'),
  ('narwhals._dask.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/dataframe.py',
   'PYMODULE'),
  ('narwhals._dask.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/group_by.py',
   'PYMODULE'),
  ('narwhals._dask.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/expr.py',
   'PYMODULE'),
  ('narwhals._pandas_like.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/group_by.py',
   'PYMODULE'),
  ('narwhals._pandas_like.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/expr.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series.py',
   'PYMODULE'),
  ('narwhals._arrow.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/typing.py',
   'PYMODULE'),
  ('narwhals._arrow.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series.py',
   'PYMODULE'),
  ('narwhals._arrow.series_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_struct.py',
   'PYMODULE'),
  ('narwhals._arrow.series_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_str.py',
   'PYMODULE'),
  ('narwhals._arrow.series_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_list.py',
   'PYMODULE'),
  ('narwhals._arrow.series_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_dt.py',
   'PYMODULE'),
  ('narwhals._arrow.series_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_cat.py',
   'PYMODULE'),
  ('narwhals._arrow.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/expr.py',
   'PYMODULE'),
  ('narwhals._arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/__init__.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_struct.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_str.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_list.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_dt.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_cat.py',
   'PYMODULE'),
  ('narwhals._pandas_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/__init__.py',
   'PYMODULE'),
  ('narwhals._dask.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/expr_str.py',
   'PYMODULE'),
  ('narwhals._dask.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/expr_dt.py',
   'PYMODULE'),
  ('narwhals._pandas_like.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/utils.py',
   'PYMODULE'),
  ('narwhals._pandas_like.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/typing.py',
   'PYMODULE'),
  ('narwhals._dask.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/utils.py',
   'PYMODULE'),
  ('narwhals._dask',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/__init__.py',
   'PYMODULE'),
  ('narwhals._duckdb.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/dataframe.py',
   'PYMODULE'),
  ('narwhals._duckdb.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/series.py',
   'PYMODULE'),
  ('narwhals._duckdb.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/group_by.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_struct.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_str.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_list.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_dt.py',
   'PYMODULE'),
  ('narwhals._duckdb.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/utils.py',
   'PYMODULE'),
  ('narwhals._duckdb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/__init__.py',
   'PYMODULE'),
  ('narwhals._polars.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/group_by.py',
   'PYMODULE'),
  ('narwhals._polars.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/expr.py',
   'PYMODULE'),
  ('narwhals._polars.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/utils.py',
   'PYMODULE'),
  ('narwhals._polars.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/series.py',
   'PYMODULE'),
  ('narwhals._polars',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/__init__.py',
   'PYMODULE'),
  ('narwhals._arrow.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/dataframe.py',
   'PYMODULE'),
  ('narwhals._arrow.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/group_by.py',
   'PYMODULE'),
  ('narwhals._pandas_like.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/dataframe.py',
   'PYMODULE'),
  ('narwhals._arrow.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/utils.py',
   'PYMODULE'),
  ('narwhals._spark_like.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/group_by.py',
   'PYMODULE'),
  ('narwhals._polars.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/namespace.py',
   'PYMODULE'),
  ('narwhals._polars.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/typing.py',
   'PYMODULE'),
  ('narwhals._pandas_like.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/namespace.py',
   'PYMODULE'),
  ('narwhals._pandas_like.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/selectors.py',
   'PYMODULE'),
  ('narwhals._ibis.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/namespace.py',
   'PYMODULE'),
  ('narwhals._ibis.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/selectors.py',
   'PYMODULE'),
  ('narwhals._duckdb.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/namespace.py',
   'PYMODULE'),
  ('narwhals._duckdb.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/selectors.py',
   'PYMODULE'),
  ('narwhals._dask.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/namespace.py',
   'PYMODULE'),
  ('narwhals._dask.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/selectors.py',
   'PYMODULE'),
  ('narwhals._arrow.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/namespace.py',
   'PYMODULE'),
  ('narwhals._arrow.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/selectors.py',
   'PYMODULE'),
  ('narwhals._constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_constants.py',
   'PYMODULE'),
  ('narwhals.stable.v1.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/selectors.py',
   'PYMODULE'),
  ('narwhals.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/selectors.py',
   'PYMODULE'),
  ('narwhals.stable.v1.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/dtypes.py',
   'PYMODULE'),
  ('narwhals.stable.v1._dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/_dtypes.py',
   'PYMODULE'),
  ('narwhals.stable',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/__init__.py',
   'PYMODULE'),
  ('narwhals.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series.py',
   'PYMODULE'),
  ('narwhals.series_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_struct.py',
   'PYMODULE'),
  ('narwhals.series_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_str.py',
   'PYMODULE'),
  ('narwhals.series_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_list.py',
   'PYMODULE'),
  ('narwhals.series_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_dt.py',
   'PYMODULE'),
  ('narwhals.series_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_cat.py',
   'PYMODULE'),
  ('narwhals.schema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/schema.py',
   'PYMODULE'),
  ('narwhals.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr.py',
   'PYMODULE'),
  ('narwhals.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_struct.py',
   'PYMODULE'),
  ('narwhals.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_str.py',
   'PYMODULE'),
  ('narwhals.expr_name',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_name.py',
   'PYMODULE'),
  ('narwhals.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_list.py',
   'PYMODULE'),
  ('narwhals.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_dt.py',
   'PYMODULE'),
  ('narwhals.expr_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_cat.py',
   'PYMODULE'),
  ('narwhals.dependencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/dependencies.py',
   'PYMODULE'),
  ('narwhals.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/dataframe.py',
   'PYMODULE'),
  ('narwhals.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/group_by.py',
   'PYMODULE'),
  ('narwhals._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_utils.py',
   'PYMODULE'),
  ('narwhals.stable.v2.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/dtypes.py',
   'PYMODULE'),
  ('narwhals.stable.v2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/__init__.py',
   'PYMODULE'),
  ('narwhals.stable.v2.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/selectors.py',
   'PYMODULE'),
  ('narwhals.stable.v2.dependencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/dependencies.py',
   'PYMODULE'),
  ('narwhals.stable.v2._namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/_namespace.py',
   'PYMODULE'),
  ('narwhals.stable.v1._namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/_namespace.py',
   'PYMODULE'),
  ('narwhals._enum',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_enum.py',
   'PYMODULE'),
  ('narwhals._typing_compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_typing_compat.py',
   'PYMODULE'),
  ('narwhals._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_exceptions.py',
   'PYMODULE'),
  ('narwhals.functions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/functions.py',
   'PYMODULE'),
  ('narwhals.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/exceptions.py',
   'PYMODULE'),
  ('narwhals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/__init__.py',
   'PYMODULE'),
  ('jsonschema.validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/validators.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/protocols.py',
   'PYMODULE'),
  ('jsonschema._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_utils.py',
   'PYMODULE'),
  ('jsonschema._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_typing.py',
   'PYMODULE'),
  ('jsonschema._types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_types.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_keywords.py',
   'PYMODULE'),
  ('jsonschema._format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_format.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/_core.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/exceptions.py',
   'PYMODULE'),
  ('jsonschema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/__init__.py',
   'PYMODULE'),
  ('altair.utils.plugin_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/plugin_registry.py',
   'PYMODULE'),
  ('altair.utils._vegafusion_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_vegafusion_data.py',
   'PYMODULE'),
  ('narwhals.stable.v1.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/typing.py',
   'PYMODULE'),
  ('altair.vegalite.data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/data.py',
   'PYMODULE'),
  ('altair.utils.data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/data.py',
   'PYMODULE'),
  ('altair.utils.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/core.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema.channels',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/channels.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/_typing.py',
   'PYMODULE'),
  ('altair.utils._dfi_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_dfi_types.py',
   'PYMODULE'),
  ('altair.utils._importers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_importers.py',
   'PYMODULE'),
  ('altair.utils.mimebundle',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/mimebundle.py',
   'PYMODULE'),
  ('altair.utils.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/html.py',
   'PYMODULE'),
  ('altair.vegalite.v5.data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/data.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/__init__.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema.mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/mixins.py',
   'PYMODULE'),
  ('altair.vegalite.v5.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/compiler.py',
   'PYMODULE'),
  ('altair.utils.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/compiler.py',
   'PYMODULE'),
  ('altair.vegalite.v5.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/api.py',
   'PYMODULE'),
  ('altair.utils._transformed_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_transformed_data.py',
   'PYMODULE'),
  ('altair.utils.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/server.py',
   'PYMODULE'),
  ('altair.utils.save',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/save.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema._config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/_config.py',
   'PYMODULE'),
  ('altair.expr.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/expr/core.py',
   'PYMODULE'),
  ('narwhals.stable.v1.dependencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/dependencies.py',
   'PYMODULE'),
  ('altair.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/typing/__init__.py',
   'PYMODULE'),
  ('altair.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/__init__.py',
   'PYMODULE'),
  ('altair.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/expr/__init__.py',
   'PYMODULE'),
  ('altair.jupyter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/__init__.py',
   'PYMODULE'),
  ('altair.jupyter.jupyter_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/jupyter_chart.py',
   'PYMODULE'),
  ('altair.utils.selection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/selection.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/core.py',
   'PYMODULE'),
  ('altair.theme',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/theme.py',
   'PYMODULE'),
  ('altair.vegalite.v5.theme',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/theme.py',
   'PYMODULE'),
  ('altair.vegalite',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/__init__.py',
   'PYMODULE'),
  ('numpy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy.strings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.char',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   '/opt/miniconda3/envs/dist/lib/python3.13/fileinput.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ctypeslib/__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ctypeslib/_ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('pandas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_version_meson.py',
   'PYMODULE'),
  ('pandas.util._tester',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_tester.py',
   'PYMODULE'),
  ('pandas.io.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/spss.py',
   'PYMODULE'),
  ('pandas.io.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/testing.py',
   'PYMODULE'),
  ('pandas._testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/_warnings.py',
   'PYMODULE'),
  ('pandas._testing._io',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/localization.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/api.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/config_init.py',
   'PYMODULE'),
  ('streamlit.web.bootstrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/bootstrap.py',
   'PYMODULE'),
  ('streamlit.web',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/__init__.py',
   'PYMODULE'),
  ('streamlit.web.server.server_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/server_util.py',
   'PYMODULE'),
  ('streamlit.runtime.secrets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/secrets.py',
   'PYMODULE'),
  ('streamlit.errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/errors.py',
   'PYMODULE'),
  ('streamlit.runtime',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.session_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/session_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.script_cache',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/script_cache.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.magic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/magic.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils.script_run_context',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/script_run_context.py',
   'PYMODULE'),
  ('streamlit.runtime.pages_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/pages_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.fragment',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/fragment.py',
   'PYMODULE'),
  ('streamlit.delta_generator_singletons',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/delta_generator_singletons.py',
   'PYMODULE'),
  ('streamlit.elements.lib.mutable_status_container',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/mutable_status_container.py',
   'PYMODULE'),
  ('streamlit.proto.Block_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Block_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.GapSize_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GapSize_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.HeightConfig_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/HeightConfig_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.WidthConfig_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/WidthConfig_pb2.py',
   'PYMODULE'),
  ('streamlit.elements.lib.layout_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/layout_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.dialog',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/dialog.py',
   'PYMODULE'),
  ('streamlit.proto.RootContainer_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/RootContainer_pb2.py',
   'PYMODULE'),
  ('streamlit.time_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/time_util.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__init__.py',
   'PYMODULE'),
  ('streamlit.proto.PageProfile_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageProfile_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ClientState_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ClientState_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.WidgetStates_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/WidgetStates_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Components_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Components_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Common_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Common_pb2.py',
   'PYMODULE'),
  ('streamlit.runtime.forward_msg_cache',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/forward_msg_cache.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils.script_requests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/script_requests.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/exceptions.py',
   'PYMODULE'),
  ('streamlit.runtime.script_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/script_data.py',
   'PYMODULE'),
  ('streamlit.proto.ForwardMsg_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ForwardMsg_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AuthRedirect_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AuthRedirect_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.SessionStatus_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/SessionStatus_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.SessionEvent_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/SessionEvent_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Exception_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Exception_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ParentMessage_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ParentMessage_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PagesChanged_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PagesChanged_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AppPage_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AppPage_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageNotFound_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageNotFound_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageInfo_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageInfo_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageConfig_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageConfig_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.NewSession_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NewSession_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Navigation_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Navigation_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Logo_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Logo_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.GitInfo_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GitInfo_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Delta_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Delta_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ArrowNamedDataSet_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ArrowNamedDataSet_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Arrow_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Arrow_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.NamedDataSet_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NamedDataSet_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DataFrame_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DataFrame_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Element_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Element_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Heading_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Heading_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Video_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Video_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.VegaLiteChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/VegaLiteChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Toast_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Toast_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.TimeInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TimeInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.LabelVisibilityMessage_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/LabelVisibilityMessage_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.TextInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TextInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.TextArea_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TextArea_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Text_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Text_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Slider_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Slider_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Skeleton_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Skeleton_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Selectbox_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Selectbox_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Radio_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Radio_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Spinner_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Spinner_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Snow_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Snow_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Progress_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Progress_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PlotlyChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PlotlyChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageLink_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageLink_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.MultiSelect_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/MultiSelect_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Metric_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Metric_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Markdown_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Markdown_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.NumberInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NumberInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.LinkButton_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/LinkButton_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Json_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Json_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Image_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Image_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.IFrame_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/IFrame_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Html_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Html_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.GraphVizChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GraphVizChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.FileUploader_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/FileUploader_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Favicon_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Favicon_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Empty_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Empty_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DocString_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DocString_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DeckGlJsonChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DeckGlJsonChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DateInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DateInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ColorPicker_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ColorPicker_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Code_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Code_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Checkbox_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Checkbox_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ChatInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ChatInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.CameraInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/CameraInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DownloadButton_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DownloadButton_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ButtonGroup_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ButtonGroup_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Button_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Button_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.BokehChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/BokehChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ArrowVegaLiteChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ArrowVegaLiteChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Balloons_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Balloons_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AudioInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AudioInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Audio_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Audio_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Alert_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Alert_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AutoRerun_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AutoRerun_pb2.py',
   'PYMODULE'),
  ('streamlit.watcher.path_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/path_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.event_based_path_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/util.py',
   'PYMODULE'),
  ('streamlit.watcher.polling_path_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/polling_path_watcher.py',
   'PYMODULE'),
  ('streamlit.url_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/url_util.py',
   'PYMODULE'),
  ('streamlit.net_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/net_util.py',
   'PYMODULE'),
  ('streamlit.cli_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/cli_util.py',
   'PYMODULE'),
  ('streamlit.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/version.py',
   'PYMODULE'),
  ('streamlit.user_info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/user_info.py',
   'PYMODULE'),
  ('streamlit.auth_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/auth_util.py',
   'PYMODULE'),
  ('streamlit.error_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/error_util.py',
   'PYMODULE'),
  ('streamlit.elements.exception',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/exception.py',
   'PYMODULE'),
  ('streamlit.deprecation_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/deprecation_util.py',
   'PYMODULE'),
  ('streamlit.cursor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/cursor.py',
   'PYMODULE'),
  ('streamlit.env_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/env_util.py',
   'PYMODULE'),
  ('streamlit.git_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/git_util.py',
   'PYMODULE'),
  ('git',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/__init__.py',
   'PYMODULE'),
  ('git.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/util.py',
   'PYMODULE'),
  ('git.repo.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/repo/base.py',
   'PYMODULE'),
  ('git.refs.symbolic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/symbolic.py',
   'PYMODULE'),
  ('git.refs.log',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/log.py',
   'PYMODULE'),
  ('git.objects.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/util.py',
   'PYMODULE'),
  ('git.objects.tree',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/tree.py',
   'PYMODULE'),
  ('git.objects.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/fun.py',
   'PYMODULE'),
  ('git.objects.tag',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/tag.py',
   'PYMODULE'),
  ('git.objects.blob',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/blob.py',
   'PYMODULE'),
  ('git.objects.commit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/commit.py',
   'PYMODULE'),
  ('git.objects.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/base.py',
   'PYMODULE'),
  ('git.refs.reference',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/reference.py',
   'PYMODULE'),
  ('gitdb.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/base.py',
   'PYMODULE'),
  ('gitdb.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/fun.py',
   'PYMODULE'),
  ('gitdb.utils.encoding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/utils/encoding.py',
   'PYMODULE'),
  ('gitdb.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/utils/__init__.py',
   'PYMODULE'),
  ('gitdb.const',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/const.py',
   'PYMODULE'),
  ('gitdb.typ',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/typ.py',
   'PYMODULE'),
  ('git.objects.submodule.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/base.py',
   'PYMODULE'),
  ('git.objects.submodule.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/util.py',
   'PYMODULE'),
  ('git.objects.submodule',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/__init__.py',
   'PYMODULE'),
  ('git.objects.submodule.root',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/root.py',
   'PYMODULE'),
  ('git.repo.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/repo/fun.py',
   'PYMODULE'),
  ('git.refs.tag',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/tag.py',
   'PYMODULE'),
  ('gitdb.exc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/exc.py',
   'PYMODULE'),
  ('gitdb.db.loose',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/loose.py',
   'PYMODULE'),
  ('gitdb.db',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/__init__.py',
   'PYMODULE'),
  ('gitdb.db.ref',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/ref.py',
   'PYMODULE'),
  ('gitdb.db.git',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/git.py',
   'PYMODULE'),
  ('gitdb.db.pack',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/pack.py',
   'PYMODULE'),
  ('gitdb.pack',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/pack.py',
   'PYMODULE'),
  ('gitdb.db.mem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/mem.py',
   'PYMODULE'),
  ('gitdb.stream',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/stream.py',
   'PYMODULE'),
  ('gitdb.db.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/base.py',
   'PYMODULE'),
  ('gitdb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/__init__.py',
   'PYMODULE'),
  ('git.index.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/util.py',
   'PYMODULE'),
  ('git.index',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/__init__.py',
   'PYMODULE'),
  ('git.index.typ',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/typ.py',
   'PYMODULE'),
  ('git.index.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/base.py',
   'PYMODULE'),
  ('git.index.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/fun.py',
   'PYMODULE'),
  ('git.remote',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/remote.py',
   'PYMODULE'),
  ('git.repo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/repo/__init__.py',
   'PYMODULE'),
  ('git.cmd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/cmd.py',
   'PYMODULE'),
  ('git.db',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/db.py',
   'PYMODULE'),
  ('git.diff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/diff.py',
   'PYMODULE'),
  ('git.refs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/__init__.py',
   'PYMODULE'),
  ('git.refs.remote',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/remote.py',
   'PYMODULE'),
  ('git.refs.head',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/head.py',
   'PYMODULE'),
  ('git.objects',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/__init__.py',
   'PYMODULE'),
  ('git.config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/config.py',
   'PYMODULE'),
  ('configparser',
   '/opt/miniconda3/envs/dist/lib/python3.13/configparser.py',
   'PYMODULE'),
  ('git.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/compat.py',
   'PYMODULE'),
  ('git.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/types.py',
   'PYMODULE'),
  ('git.exc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/exc.py',
   'PYMODULE'),
  ('gitdb.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/util.py',
   'PYMODULE'),
  ('smmap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/__init__.py',
   'PYMODULE'),
  ('smmap.buf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/buf.py',
   'PYMODULE'),
  ('smmap.mman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/mman.py',
   'PYMODULE'),
  ('smmap.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/util.py',
   'PYMODULE'),
  ('streamlit.watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/__init__.py',
   'PYMODULE'),
  ('streamlit.watcher.local_sources_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/local_sources_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.folder_black_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/folder_black_list.py',
   'PYMODULE'),
  ('streamlit.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/util.py',
   'PYMODULE'),
  ('streamlit.type_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/type_util.py',
   'PYMODULE'),
  ('pydeck',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/__init__.py',
   'PYMODULE'),
  ('pydeck.settings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/settings.py',
   'PYMODULE'),
  ('pydeck._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/_version.py',
   'PYMODULE'),
  ('pydeck.nbextension',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/nbextension/__init__.py',
   'PYMODULE'),
  ('pydeck.bindings.map_styles',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/map_styles.py',
   'PYMODULE'),
  ('pydeck.bindings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/__init__.py',
   'PYMODULE'),
  ('pydeck.bindings.view_state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/view_state.py',
   'PYMODULE'),
  ('pydeck.bindings.json_tools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/json_tools.py',
   'PYMODULE'),
  ('pydeck.types.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/base.py',
   'PYMODULE'),
  ('pydeck.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/__init__.py',
   'PYMODULE'),
  ('pydeck.types.function',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/function.py',
   'PYMODULE'),
  ('pydeck.types.image',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/image.py',
   'PYMODULE'),
  ('pydeck.types.string',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/string.py',
   'PYMODULE'),
  ('pydeck.bindings.view',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/view.py',
   'PYMODULE'),
  ('pydeck.bindings.light_settings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/light_settings.py',
   'PYMODULE'),
  ('pydeck.bindings.layer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/layer.py',
   'PYMODULE'),
  ('pydeck.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/exceptions/__init__.py',
   'PYMODULE'),
  ('pydeck.exceptions.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/exceptions/exceptions.py',
   'PYMODULE'),
  ('pydeck.data_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/__init__.py',
   'PYMODULE'),
  ('pydeck.data_utils.color_scales',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/color_scales.py',
   'PYMODULE'),
  ('pydeck.data_utils.type_checking',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/type_checking.py',
   'PYMODULE'),
  ('pydeck.data_utils.viewport_helpers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/viewport_helpers.py',
   'PYMODULE'),
  ('pydeck.bindings.deck',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/deck.py',
   'PYMODULE'),
  ('pydeck.widget',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/__init__.py',
   'PYMODULE'),
  ('pydeck.widget.widget',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/widget.py',
   'PYMODULE'),
  ('pydeck.widget.debounce',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/debounce.py',
   'PYMODULE'),
  ('pydeck.widget._frontend',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/_frontend.py',
   'PYMODULE'),
  ('pydeck.frontend_semver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/frontend_semver.py',
   'PYMODULE'),
  ('pydeck.data_utils.binary_transfer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/binary_transfer.py',
   'PYMODULE'),
  ('pydeck.bindings.base_map_provider',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/base_map_provider.py',
   'PYMODULE'),
  ('pydeck.io.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/io/html.py',
   'PYMODULE'),
  ('pydeck.io',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/io/__init__.py',
   'PYMODULE'),
  ('streamlit.string_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/string_util.py',
   'PYMODULE'),
  ('streamlit.material_icon_names',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/material_icon_names.py',
   'PYMODULE'),
  ('streamlit.emojis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/emojis.py',
   'PYMODULE'),
  ('streamlit.source_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/source_util.py',
   'PYMODULE'),
  ('streamlit.file_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/file_util.py',
   'PYMODULE'),
  ('streamlit.config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/config.py',
   'PYMODULE'),
  ('streamlit.config_option',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/config_option.py',
   'PYMODULE'),
  ('streamlit.development',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/development.py',
   'PYMODULE'),
  ('streamlit.config_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/config_util.py',
   'PYMODULE'),
  ('streamlit.logger',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/logger.py',
   'PYMODULE'),
  ('streamlit.web.server.stats_request_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/stats_request_handler.py',
   'PYMODULE'),
  ('streamlit.proto.openmetrics_data_model_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/openmetrics_data_model_pb2.py',
   'PYMODULE'),
  ('streamlit.web.server.upload_file_request_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/upload_file_request_handler.py',
   'PYMODULE'),
  ('streamlit.runtime.memory_uploaded_file_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/memory_uploaded_file_manager.py',
   'PYMODULE'),
  ('streamlit.web.server.component_request_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/component_request_handler.py',
   'PYMODULE'),
  ('streamlit.components.types.base_component_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/types/base_component_registry.py',
   'PYMODULE'),
  ('streamlit.components.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/types/__init__.py',
   'PYMODULE'),
  ('streamlit.components',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/__init__.py',
   'PYMODULE'),
  ('streamlit.components.types.base_custom_component',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/types/base_custom_component.py',
   'PYMODULE'),
  ('streamlit.runtime.state.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/common.py',
   'PYMODULE'),
  ('streamlit.web.server.media_file_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/media_file_handler.py',
   'PYMODULE'),
  ('streamlit.runtime.memory_media_file_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/memory_media_file_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.media_file_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/media_file_storage.py',
   'PYMODULE'),
  ('streamlit.web.server.routes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/routes.py',
   'PYMODULE'),
  ('streamlit.web.server.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/server.py',
   'PYMODULE'),
  ('streamlit.hello.streamlit_app',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/hello/streamlit_app.py',
   'PYMODULE'),
  ('streamlit.hello',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/hello/__init__.py',
   'PYMODULE'),
  ('streamlit.web.server.oauth_authlib_routes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/oauth_authlib_routes.py',
   'PYMODULE'),
  ('streamlit.web.server.oidc_mixin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/oidc_mixin.py',
   'PYMODULE'),
  ('streamlit.web.server.authlib_tornado_integration',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/authlib_tornado_integration.py',
   'PYMODULE'),
  ('streamlit.web.server.browser_websocket_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/browser_websocket_handler.py',
   'PYMODULE'),
  ('streamlit.proto.BackMsg_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/BackMsg_pb2.py',
   'PYMODULE'),
  ('streamlit.web.server.app_static_file_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/app_static_file_handler.py',
   'PYMODULE'),
  ('streamlit.web.cache_storage_manager_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/cache_storage_manager_config.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.cache_storage_protocol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/cache_storage_protocol.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.local_disk_cache_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/local_disk_cache_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.in_memory_cache_storage_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/in_memory_cache_storage_wrapper.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_utils.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_type',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_type.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.hashing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/hashing.py',
   'PYMODULE'),
  ('PIL.Image',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   '/opt/miniconda3/envs/dist/lib/python3.13/colorsys.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL._util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cached_message_replay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cached_message_replay.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_errors.py',
   'PYMODULE'),
  ('streamlit.elements.spinner',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/spinner.py',
   'PYMODULE'),
  ('streamlit.dataframe_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/dataframe_util.py',
   'PYMODULE'),
  ('cachetools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/__init__.py',
   'PYMODULE'),
  ('cachetools._cachedmethod',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/_cachedmethod.py',
   'PYMODULE'),
  ('cachetools._cached',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/_cached.py',
   'PYMODULE'),
  ('cachetools.keys',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/keys.py',
   'PYMODULE'),
  ('streamlit.runtime.runtime_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/runtime_util.py',
   'PYMODULE'),
  ('streamlit.runtime.memory_session_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/memory_session_storage.py',
   'PYMODULE'),
  ('streamlit.web.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.stats',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/stats.py',
   'PYMODULE'),
  ('streamlit.runtime.runtime',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/runtime.py',
   'PYMODULE'),
  ('streamlit.runtime.websocket_session_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/websocket_session_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.media_file_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/media_file_manager.py',
   'PYMODULE'),
  ('streamlit.components.lib.local_component_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/lib/local_component_registry.py',
   'PYMODULE'),
  ('streamlit.components.lib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/lib/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.app_session',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/app_session.py',
   'PYMODULE'),
  ('streamlit.runtime.forward_msg_queue',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/forward_msg_queue.py',
   'PYMODULE'),
  ('streamlit.runtime.metrics_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/metrics_util.py',
   'PYMODULE'),
  ('timeit', '/opt/miniconda3/envs/dist/lib/python3.13/timeit.py', 'PYMODULE'),
  ('streamlit.proto',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.lib.policies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/policies.py',
   'PYMODULE'),
  ('streamlit.elements.lib.form_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/form_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.column_config_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/column_config_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.dicttools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/dicttools.py',
   'PYMODULE'),
  ('streamlit.elements.lib.column_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/column_types.py',
   'PYMODULE'),
  ('streamlit.elements.lib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.form',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/form.py',
   'PYMODULE'),
  ('streamlit.elements',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/__init__.py',
   'PYMODULE'),
  ('streamlit.delta_generator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/delta_generator.py',
   'PYMODULE'),
  ('streamlit.elements.lib.built_in_chart_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/built_in_chart_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.color_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/color_util.py',
   'PYMODULE'),
  ('streamlit.elements.write',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/write.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.time_widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/time_widgets.py',
   'PYMODULE'),
  ('streamlit.elements.lib.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/utils.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.text_widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/text_widgets.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.slider',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/slider.py',
   'PYMODULE'),
  ('streamlit.elements.lib.js_number',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/js_number.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.selectbox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/selectbox.py',
   'PYMODULE'),
  ('streamlit.elements.lib.options_selector_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/options_selector_utils.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.select_slider',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/select_slider.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.radio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/radio.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.number_input',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/number_input.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.multiselect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/multiselect.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.file_uploader',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/file_uploader.py',
   'PYMODULE'),
  ('streamlit.elements.lib.file_uploader_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/file_uploader_utils.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.data_editor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/data_editor.py',
   'PYMODULE'),
  ('streamlit.elements.lib.pandas_styler_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/pandas_styler_utils.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.color_picker',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/color_picker.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.checkbox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/checkbox.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.chat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/chat.py',
   'PYMODULE'),
  ('streamlit.runtime.state.session_state_proxy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/session_state_proxy.py',
   'PYMODULE'),
  ('streamlit.runtime.state.session_state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/session_state.py',
   'PYMODULE'),
  ('streamlit.vendor.pympler.asizeof',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/vendor/pympler/asizeof.py',
   'PYMODULE'),
  ('streamlit.vendor.pympler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/vendor/pympler/__init__.py',
   'PYMODULE'),
  ('streamlit.vendor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/vendor/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.state.query_params',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/query_params.py',
   'PYMODULE'),
  ('streamlit.runtime.state.safe_session_state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/safe_session_state.py',
   'PYMODULE'),
  ('streamlit.elements.lib.image_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/image_utils.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.camera_input',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/camera_input.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.button_group',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/button_group.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.button',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/button.py',
   'PYMODULE'),
  ('streamlit.navigation.page',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/navigation/page.py',
   'PYMODULE'),
  ('streamlit.navigation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/navigation/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.audio_input',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/audio_input.py',
   'PYMODULE'),
  ('streamlit.elements.vega_charts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/vega_charts.py',
   'PYMODULE'),
  ('streamlit.elements.toast',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/toast.py',
   'PYMODULE'),
  ('streamlit.elements.text',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/text.py',
   'PYMODULE'),
  ('streamlit.elements.snow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/snow.py',
   'PYMODULE'),
  ('streamlit.elements.pyplot',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/pyplot.py',
   'PYMODULE'),
  ('streamlit.elements.progress',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/progress.py',
   'PYMODULE'),
  ('streamlit.elements.plotly_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/plotly_chart.py',
   'PYMODULE'),
  ('streamlit.elements.lib.streamlit_plotly_theme',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/streamlit_plotly_theme.py',
   'PYMODULE'),
  ('streamlit.elements.metric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/metric.py',
   'PYMODULE'),
  ('streamlit.elements.media',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/media.py',
   'PYMODULE'),
  ('wave', '/opt/miniconda3/envs/dist/lib/python3.13/wave.py', 'PYMODULE'),
  ('streamlit.elements.lib.subtitle_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/subtitle_utils.py',
   'PYMODULE'),
  ('streamlit.elements.markdown',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/markdown.py',
   'PYMODULE'),
  ('streamlit.elements.map',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/map.py',
   'PYMODULE'),
  ('streamlit.elements.layouts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/layouts.py',
   'PYMODULE'),
  ('streamlit.elements.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/json.py',
   'PYMODULE'),
  ('streamlit.elements.image',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/image.py',
   'PYMODULE'),
  ('streamlit.elements.iframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/iframe.py',
   'PYMODULE'),
  ('streamlit.elements.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/html.py',
   'PYMODULE'),
  ('streamlit.elements.heading',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/heading.py',
   'PYMODULE'),
  ('streamlit.elements.graphviz_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/graphviz_chart.py',
   'PYMODULE'),
  ('streamlit.elements.empty',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/empty.py',
   'PYMODULE'),
  ('streamlit.elements.doc_string',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/doc_string.py',
   'PYMODULE'),
  ('streamlit.elements.deck_gl_json_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/deck_gl_json_chart.py',
   'PYMODULE'),
  ('streamlit.elements.code',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/code.py',
   'PYMODULE'),
  ('streamlit.elements.bokeh_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/bokeh_chart.py',
   'PYMODULE'),
  ('streamlit.elements.balloons',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/balloons.py',
   'PYMODULE'),
  ('streamlit.elements.arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/arrow.py',
   'PYMODULE'),
  ('streamlit.elements.alert',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/alert.py',
   'PYMODULE'),
  ('streamlit.components.v1.components',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/components.py',
   'PYMODULE'),
  ('streamlit.components.v1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/__init__.py',
   'PYMODULE'),
  ('streamlit.components.v1.component_arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/component_arrow.py',
   'PYMODULE'),
  ('streamlit.components.v1.custom_component',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/custom_component.py',
   'PYMODULE'),
  ('streamlit.components.v1.component_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/component_registry.py',
   'PYMODULE'),
  ('streamlit.runtime.uploaded_file_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/uploaded_file_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.caching',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.legacy_cache_api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/legacy_cache_api.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_resource_api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_resource_api.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_data_api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_data_api.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.dummy_cache_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/dummy_cache_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.state.widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/widgets.py',
   'PYMODULE'),
  ('streamlit.runtime.state.query_params_proxy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/query_params_proxy.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.script_runner',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/script_runner.py',
   'PYMODULE'),
  ('streamlit.commands.navigation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/navigation.py',
   'PYMODULE'),
  ('streamlit.commands',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.exec_code',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/exec_code.py',
   'PYMODULE'),
  ('streamlit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/__init__.py',
   'PYMODULE'),
  ('streamlit.commands.execution_control',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/execution_control.py',
   'PYMODULE'),
  ('streamlit.commands.page_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/page_config.py',
   'PYMODULE'),
  ('streamlit.commands.logo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/logo.py',
   'PYMODULE'),
  ('streamlit.commands.echo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/echo.py',
   'PYMODULE'),
  ('streamlit.column_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/column_config.py',
   'PYMODULE'),
  ('streamlit.commands.experimental_query_params',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/experimental_query_params.py',
   'PYMODULE'),
  ('streamlit.runtime.context',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/context.py',
   'PYMODULE'),
  ('streamlit.runtime.context_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/context_util.py',
   'PYMODULE'),
  ('streamlit.runtime.connection_factory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/connection_factory.py',
   'PYMODULE'),
  ('streamlit.connections',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/__init__.py',
   'PYMODULE'),
  ('streamlit.connections.sql_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/sql_connection.py',
   'PYMODULE'),
  ('tenacity.retry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/retry.py',
   'PYMODULE'),
  ('tenacity',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/__init__.py',
   'PYMODULE'),
  ('tenacity.tornadoweb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/tornadoweb.py',
   'PYMODULE'),
  ('tenacity.asyncio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/asyncio/__init__.py',
   'PYMODULE'),
  ('tenacity.asyncio.retry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/asyncio/retry.py',
   'PYMODULE'),
  ('tenacity.before_sleep',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/before_sleep.py',
   'PYMODULE'),
  ('tenacity.after',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/after.py',
   'PYMODULE'),
  ('tenacity.before',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/before.py',
   'PYMODULE'),
  ('tenacity.wait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/wait.py',
   'PYMODULE'),
  ('tenacity.stop',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/stop.py',
   'PYMODULE'),
  ('tenacity.nap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/nap.py',
   'PYMODULE'),
  ('tenacity._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/_utils.py',
   'PYMODULE'),
  ('streamlit.connections.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/util.py',
   'PYMODULE'),
  ('streamlit.connections.snowpark_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/snowpark_connection.py',
   'PYMODULE'),
  ('streamlit.connections.snowflake_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/snowflake_connection.py',
   'PYMODULE'),
  ('streamlit.connections.base_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/base_connection.py',
   'PYMODULE'),
  ('streamlit.elements.dialog_decorator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/dialog_decorator.py',
   'PYMODULE'),
  ('stringprep',
   '/opt/miniconda3/envs/dist/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   '/opt/miniconda3/envs/dist/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('streamlit.web.cli',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/cli.py',
   'PYMODULE'),
  ('streamlit.temporary_directory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/temporary_directory.py',
   'PYMODULE'),
  ('streamlit.runtime.credentials',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/credentials.py',
   'PYMODULE'),
  ('ui',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/ui.py',
   'PYMODULE')],
 [('pyarrow/libarrow.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.dylib',
   'BINARY'),
  ('pyarrow/libarrow_flight.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_flight.2100.dylib',
   'BINARY'),
  ('pyarrow/libparquet.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libparquet.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_compute.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_compute.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_dataset.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_dataset.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2100.0.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.dylib',
   'BINARY'),
  ('pyarrow/libarrow_acero.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_acero.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_substrait.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_substrait.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_flight.2100.0.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_flight.2100.0.0.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python.2100.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python.2100.dylib',
   'BINARY'),
  ('pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/libarrow_python_parquet_encryption.2100.0.0.dylib',
   'BINARY'),
  ('libpython3.13.dylib',
   '/opt/miniconda3/envs/dist/lib/libpython3.13.dylib',
   'BINARY'),
  ('pyarrow/lib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.cpython-313-darwin.so',
   'BINARY'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_posixshmem.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_multiprocessing.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/pyexpat.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_scproxy.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/termios.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_ssl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/mmap.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_ctypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_queue.cpython-313-darwin.so',
   'EXTENSION'),
  ('google/_upb/_message.abi3.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/_upb/_message.abi3.so',
   'EXTENSION'),
  ('pyarrow/_substrait.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_substrait.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_s3fs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_s3fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_orc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_hdfs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_hdfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_fs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_fs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_flight.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_flight.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_feather.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_feather.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_csv.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_compute.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_asyncio.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/md__mypyc.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/md.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_uuid.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_acero.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_acero.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_gcsfs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_gcsfs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_azurefs.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_azurefs.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_parquet.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_dataset_orc.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_orc.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/hashing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/nattype.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/period.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/period.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/offsets.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/strptime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/conversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/timezones.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslib.cpython-313-darwin.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/markupsafe/_speedups.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/properties.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/properties.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/writers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/writers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/ops.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/parsers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/parsers.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/json.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/readline.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_sqlite3.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/join.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/join.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/aggregations.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/window/aggregations.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/window/indexers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/window/indexers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_multiarray_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/_umath_linalg.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/indexing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/indexing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sparse.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/sparse.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/reshape.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/reshape.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_elementtree.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_elementtree.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/interval.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/interval.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/groupby.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/groupby.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/tzconversion.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/ccalendar.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/ops_dispatch.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/timestamps.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/np_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/fields.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/dtypes.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/parsing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/timedeltas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/index.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/index.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/hashtable.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/hashtable.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/algos.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/algos.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/vectorized.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/arrays.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/arrays.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/missing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/missing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/lib.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/lib.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/internals.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/internals.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/pandas_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/pandas_parser.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/pandas_parser.cpython-313-darwin.so',
   'EXTENSION'),
  ('pyarrow/_parquet_encryption.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet_encryption.cpython-313-darwin.so',
   'EXTENSION'),
  ('tornado/speedups.abi3.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/speedups.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_curses.cpython-313-darwin.so',
   'EXTENSION'),
  ('rpds/rpds.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/rpds/rpds.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_multiarray_tests.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/mtrand.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/bit_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_sfc64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_philox.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_pcg64.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_mt19937.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_generator.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_common.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_bounded_integers.cpython-313-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/_pocketfft_umath.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/cmath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/cmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/tslibs/base.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/base.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/testing.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/testing.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/sas.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/sas.cpython-313-darwin.so',
   'EXTENSION'),
  ('pandas/_libs/byteswap.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/byteswap.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_webp.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imagingtk.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_avif.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_avif.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imagingcms.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imagingmath.cpython-313-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_imaging.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_testcapi.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_testcapi.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/opt/miniconda3/envs/dist/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('libmpdec.4.dylib',
   '/opt/miniconda3/envs/dist/lib/libmpdec.4.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/miniconda3/envs/dist/lib/libcrypto.3.dylib',
   'BINARY'),
  ('libz.1.dylib', '/opt/miniconda3/envs/dist/lib/libz.1.dylib', 'BINARY'),
  ('libbz2.dylib', '/opt/miniconda3/envs/dist/lib/libbz2.dylib', 'BINARY'),
  ('libexpat.1.dylib',
   '/opt/miniconda3/envs/dist/lib/libexpat.1.dylib',
   'BINARY'),
  ('libssl.3.dylib', '/opt/miniconda3/envs/dist/lib/libssl.3.dylib', 'BINARY'),
  ('libffi.8.dylib', '/opt/miniconda3/envs/dist/lib/libffi.8.dylib', 'BINARY'),
  ('libreadline.8.dylib',
   '/opt/miniconda3/envs/dist/lib/libreadline.8.dylib',
   'BINARY'),
  ('libsqlite3.dylib',
   '/opt/miniconda3/envs/dist/lib/libsqlite3.dylib',
   'BINARY'),
  ('libncursesw.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libncursesw.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libavif.16.3.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libavif.16.3.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libjpeg.62.4.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libjpeg.62.4.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.3.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libopenjp2.2.5.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('libncurses.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libncurses.6.dylib',
   'BINARY'),
  ('libicui18n.75.dylib',
   '/opt/miniconda3/envs/dist/lib/libicui18n.75.dylib',
   'BINARY'),
  ('libicuuc.75.dylib',
   '/opt/miniconda3/envs/dist/lib/libicuuc.75.dylib',
   'BINARY'),
  ('libtinfow.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libtinfow.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/.dylibs/libXau.6.dylib',
   'BINARY'),
  ('libtinfo.6.dylib',
   '/opt/miniconda3/envs/dist/lib/libtinfo.6.dylib',
   'BINARY'),
  ('libicudata.75.dylib',
   '/opt/miniconda3/envs/dist/lib/libicudata.75.dylib',
   'BINARY')],
 [],
 [],
 [('ui.py',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/ui.py',
   'DATA'),
  ('certifi/cacert.pem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/cacert.pem',
   'DATA'),
  ('certifi/py.typed',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/py.typed',
   'DATA'),
  ('pandas/io/formats/templates/html.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/html.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_table.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/html_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_longtable.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/latex_longtable.tpl',
   'DATA'),
  ('pandas/io/formats/templates/html_style.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/html_style.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/latex.tpl',
   'DATA'),
  ('pandas/io/formats/templates/latex_table.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/latex_table.tpl',
   'DATA'),
  ('pandas/io/formats/templates/string.tpl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/templates/string.tpl',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz/zoneinfo/iso3166.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/iso3166.tab',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Monticello',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Monticello',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bissau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bissau',
   'DATA'),
  ('pytz/zoneinfo/America/Mazatlan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Mazatlan',
   'DATA'),
  ('pytz/zoneinfo/EST5EDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/EST5EDT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Harare',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Harare',
   'DATA'),
  ('pytz/zoneinfo/America/Mendoza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT0',
   'DATA'),
  ('pytz/zoneinfo/NZ-CHAT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/NZ-CHAT',
   'DATA'),
  ('pytz/zoneinfo/Australia/Melbourne',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Melbourne',
   'DATA'),
  ('pytz/zoneinfo/Asia/Khandyga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Khandyga',
   'DATA'),
  ('pytz/zoneinfo/Universal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Grand_Turk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Grand_Turk',
   'DATA'),
  ('pytz/zoneinfo/America/Winnipeg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Winnipeg',
   'DATA'),
  ('pytz/zoneinfo/Greenwich',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/America/Lower_Princes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Lower_Princes',
   'DATA'),
  ('pytz/zoneinfo/Europe/Athens',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Athens',
   'DATA'),
  ('pytz/zoneinfo/America/Tegucigalpa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Tegucigalpa',
   'DATA'),
  ('pytz/zoneinfo/America/Rio_Branco',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rio_Branco',
   'DATA'),
  ('pytz/zoneinfo/America/Resolute',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Resolute',
   'DATA'),
  ('pytz/zoneinfo/Africa/El_Aaiun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/El_Aaiun',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kyiv',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kyiv',
   'DATA'),
  ('pytz/zoneinfo/America/La_Paz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/La_Paz',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sarajevo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sarajevo',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/La_Rioja',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/La_Rioja',
   'DATA'),
  ('pytz/zoneinfo/Brazil/DeNoronha',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/DeNoronha',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Yap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Yap',
   'DATA'),
  ('pytz/zoneinfo/America/Montevideo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Montevideo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Freetown',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Freetown',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulaanbaatar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulaanbaatar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qostanay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qostanay',
   'DATA'),
  ('pytz/zoneinfo/Libya',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Libya',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kampala',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kampala',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macau',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kigali',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kigali',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ceuta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ceuta',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmera',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmera',
   'DATA'),
  ('pytz/zoneinfo/US/Hawaii',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Hawaii',
   'DATA'),
  ('pytz/zoneinfo/America/Eirunepe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Eirunepe',
   'DATA'),
  ('pytz/zoneinfo/Australia/Darwin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Darwin',
   'DATA'),
  ('pytz/zoneinfo/Australia/Victoria',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Victoria',
   'DATA'),
  ('pytz/zoneinfo/Asia/Magadan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Magadan',
   'DATA'),
  ('pytz/zoneinfo/MST7MDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/MST7MDT',
   'DATA'),
  ('pytz/zoneinfo/US/Michigan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Michigan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-3',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-3',
   'DATA'),
  ('pytz/zoneinfo/America/Tijuana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Tijuana',
   'DATA'),
  ('pytz/zoneinfo/Turkey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Turkey',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dubai',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dubai',
   'DATA'),
  ('pytz/zoneinfo/Australia/South',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/South',
   'DATA'),
  ('pytz/zoneinfo/America/Catamarca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+9',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+9',
   'DATA'),
  ('pytz/zoneinfo/Europe/Moscow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Moscow',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maputo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maputo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+8',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+8',
   'DATA'),
  ('pytz/zoneinfo/America/Virgin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Virgin',
   'DATA'),
  ('pytz/zoneinfo/Europe/Oslo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Oslo',
   'DATA'),
  ('pytz/zoneinfo/Asia/Sakhalin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Sakhalin',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tunis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tunis',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Casey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Casey',
   'DATA'),
  ('pytz/zoneinfo/America/Louisville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Louisville',
   'DATA'),
  ('pytz/zoneinfo/America/Noronha',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Noronha',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Velho',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Velho',
   'DATA'),
  ('pytz/zoneinfo/America/Cordoba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mahe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mahe',
   'DATA'),
  ('pytz/zoneinfo/Europe/Saratov',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Saratov',
   'DATA'),
  ('pytz/zoneinfo/Asia/Omsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Omsk',
   'DATA'),
  ('pytz/zoneinfo/America/Cancun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cancun',
   'DATA'),
  ('pytz/zoneinfo/Asia/Gaza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Gaza',
   'DATA'),
  ('pytz/zoneinfo/Navajo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Navajo',
   'DATA'),
  ('pytz/zoneinfo/America/Sao_Paulo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Sao_Paulo',
   'DATA'),
  ('pytz/zoneinfo/America/Guatemala',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guatemala',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-12',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-12',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Port_Moresby',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Port_Moresby',
   'DATA'),
  ('pytz/zoneinfo/Singapore',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Singapore',
   'DATA'),
  ('pytz/zoneinfo/America/Rankin_Inlet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rankin_Inlet',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faeroe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faeroe',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tehran',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tehran',
   'DATA'),
  ('pytz/zoneinfo/America/Rosario',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rosario',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Tucuman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Tucuman',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chita',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chita',
   'DATA'),
  ('pytz/zoneinfo/Asia/Phnom_Penh',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Phnom_Penh',
   'DATA'),
  ('pytz/zoneinfo/America/Monterrey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Monterrey',
   'DATA'),
  ('pytz/zoneinfo/America/Ojinaga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Ojinaga',
   'DATA'),
  ('pytz/zoneinfo/Europe/Dublin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Dublin',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Center',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Center',
   'DATA'),
  ('pytz/zoneinfo/PRC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/PRC',
   'DATA'),
  ('pytz/zoneinfo/America/Kralendijk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Kralendijk',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-10',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-10',
   'DATA'),
  ('pytz/zoneinfo/America/Hermosillo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Hermosillo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nairobi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nairobi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lome',
   'DATA'),
  ('pytz/zoneinfo/America/Cayman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Stockholm',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Stockholm',
   'DATA'),
  ('pytz/zoneinfo/Zulu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-8',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-8',
   'DATA'),
  ('pytz/zoneinfo/Australia/Eucla',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Eucla',
   'DATA'),
  ('pytz/zoneinfo/Chile/EasterIsland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Chile/EasterIsland',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bangui',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bangui',
   'DATA'),
  ('pytz/zoneinfo/America/Adak',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Adak',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/DumontDUrville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/DumontDUrville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Colombo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Colombo',
   'DATA'),
  ('pytz/zoneinfo/America/Guyana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guyana',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Palau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Palau',
   'DATA'),
  ('pytz/zoneinfo/Mexico/General',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/General',
   'DATA'),
  ('pytz/zoneinfo/Canada/Newfoundland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Newfoundland',
   'DATA'),
  ('pytz/zoneinfo/America/Detroit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Detroit',
   'DATA'),
  ('pytz/zoneinfo/Asia/Saigon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Saigon',
   'DATA'),
  ('pytz/zoneinfo/America/Port_of_Spain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Port_of_Spain',
   'DATA'),
  ('pytz/zoneinfo/GMT+0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dushanbe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dushanbe',
   'DATA'),
  ('pytz/zoneinfo/Jamaica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Africa/Tripoli',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Tripoli',
   'DATA'),
  ('pytz/zoneinfo/America/Cayenne',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cayenne',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson',
   'DATA'),
  ('pytz/zoneinfo/America/Santa_Isabel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santa_Isabel',
   'DATA'),
  ('pytz/zoneinfo/Brazil/West',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/West',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wallis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wallis',
   'DATA'),
  ('pytz/zoneinfo/Arctic/Longyearbyen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Arctic/Longyearbyen',
   'DATA'),
  ('pytz/zoneinfo/Africa/Casablanca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Casablanca',
   'DATA'),
  ('pytz/zoneinfo/Europe/Monaco',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Monaco',
   'DATA'),
  ('pytz/zoneinfo/America/Grenada',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Grenada',
   'DATA'),
  ('pytz/zoneinfo/Indian/Comoro',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Comoro',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Johnston',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Johnston',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tongatapu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tongatapu',
   'DATA'),
  ('pytz/zoneinfo/America/Halifax',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Halifax',
   'DATA'),
  ('pytz/zoneinfo/Africa/Banjul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Banjul',
   'DATA'),
  ('pytz/zoneinfo/America/Santarem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santarem',
   'DATA'),
  ('pytz/zoneinfo/America/Guayaquil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guayaquil',
   'DATA'),
  ('pytz/zoneinfo/Asia/Samarkand',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Samarkand',
   'DATA'),
  ('pytz/zoneinfo/America/Nuuk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nuuk',
   'DATA'),
  ('pytz/zoneinfo/America/Campo_Grande',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Campo_Grande',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lindeman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lindeman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bratislava',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bratislava',
   'DATA'),
  ('pytz/zoneinfo/America/Thule',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Thule',
   'DATA'),
  ('pytz/zoneinfo/America/Nome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nome',
   'DATA'),
  ('pytz/zoneinfo/America/Asuncion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Asuncion',
   'DATA'),
  ('pytz/zoneinfo/Asia/Singapore',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Singapore',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tbilisi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tbilisi',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ulan_Bator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ulan_Bator',
   'DATA'),
  ('pytz/zoneinfo/Asia/Shanghai',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Shanghai',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tiraspol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tiraspol',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pohnpei',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pohnpei',
   'DATA'),
  ('pytz/zoneinfo/Europe/Samara',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Samara',
   'DATA'),
  ('pytz/zoneinfo/America/Sitka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Sitka',
   'DATA'),
  ('pytz/zoneinfo/America/Creston',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Creston',
   'DATA'),
  ('pytz/zoneinfo/US/Samoa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ulyanovsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ulyanovsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Riyadh',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Riyadh',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Indianapolis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guam',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guam',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yangon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yangon',
   'DATA'),
  ('pytz/zoneinfo/Africa/Algiers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Algiers',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yekaterinburg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yekaterinburg',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kwajalein',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Europe/Nicosia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novosibirsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novosibirsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Helsinki',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Helsinki',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Reykjavik',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Reykjavik',
   'DATA'),
  ('pytz/zoneinfo/Africa/Brazzaville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Brazzaville',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Buenos_Aires',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Salta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Salta',
   'DATA'),
  ('pytz/zoneinfo/Africa/Douala',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Douala',
   'DATA'),
  ('pytz/zoneinfo/America/Montreal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Montreal',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia_Banderas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia_Banderas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bahrain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bahrain',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+10',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+10',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kathmandu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kathmandu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ust-Nera',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ust-Nera',
   'DATA'),
  ('pytz/zoneinfo/America/Ciudad_Juarez',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Ciudad_Juarez',
   'DATA'),
  ('pytz/zoneinfo/America/Toronto',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Toronto',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+6',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+6',
   'DATA'),
  ('pytz/zoneinfo/Australia/Queensland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Queensland',
   'DATA'),
  ('pytz/zoneinfo/Canada/Pacific',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Macquarie',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Macquarie',
   'DATA'),
  ('pytz/zoneinfo/Europe/Volgograd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Volgograd',
   'DATA'),
  ('pytz/zoneinfo/America/Iqaluit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Iqaluit',
   'DATA'),
  ('pytz/zoneinfo/America/St_Lucia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Lucia',
   'DATA'),
  ('pytz/zoneinfo/Iceland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Iceland',
   'DATA'),
  ('pytz/zoneinfo/Africa/Monrovia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Monrovia',
   'DATA'),
  ('pytz/zoneinfo/Canada/Saskatchewan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Saskatchewan',
   'DATA'),
  ('pytz/zoneinfo/Australia/ACT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/ACT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dar_es_Salaam',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dar_es_Salaam',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Nauru',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Nauru',
   'DATA'),
  ('pytz/zoneinfo/Etc/Greenwich',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Greenwich',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mbabane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mbabane',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Luis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Luis',
   'DATA'),
  ('pytz/zoneinfo/leapseconds',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/leapseconds',
   'DATA'),
  ('pytz/zoneinfo/CST6CDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/CST6CDT',
   'DATA'),
  ('pytz/zoneinfo/America/Blanc-Sablon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Blanc-Sablon',
   'DATA'),
  ('pytz/zoneinfo/Europe/Ljubljana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Ljubljana',
   'DATA'),
  ('pytz/zoneinfo/Asia/Muscat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Muscat',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tirane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tirane',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vincennes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vincennes',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chongqing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chongqing',
   'DATA'),
  ('pytz/zoneinfo/America/Regina',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Regina',
   'DATA'),
  ('pytz/zoneinfo/America/Moncton',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Moncton',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Vevay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Vevay',
   'DATA'),
  ('pytz/zoneinfo/America/Coyhaique',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Coyhaique',
   'DATA'),
  ('pytz/zoneinfo/Asia/Famagusta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Famagusta',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Ponape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Ponape',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kosrae',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kosrae',
   'DATA'),
  ('pytz/zoneinfo/Asia/Krasnoyarsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Krasnoyarsk',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Nelson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Nelson',
   'DATA'),
  ('pytz/zoneinfo/Africa/Asmara',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Asmara',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lubumbashi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lubumbashi',
   'DATA'),
  ('pytz/zoneinfo/America/Menominee',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Menominee',
   'DATA'),
  ('pytz/zoneinfo/CET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/CET',
   'DATA'),
  ('pytz/zoneinfo/Indian/Christmas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Christmas',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaNorte',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaNorte',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bujumbura',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bujumbura',
   'DATA'),
  ('pytz/zoneinfo/America/Fortaleza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Fortaleza',
   'DATA'),
  ('pytz/zoneinfo/Canada/Central',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Central',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jerusalem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jerusalem',
   'DATA'),
  ('pytz/zoneinfo/Egypt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Egypt',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Rarotonga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Rarotonga',
   'DATA'),
  ('pytz/zoneinfo/America/Chihuahua',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Chihuahua',
   'DATA'),
  ('pytz/zoneinfo/Europe/Skopje',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Skopje',
   'DATA'),
  ('pytz/zoneinfo/America/Anchorage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Anchorage',
   'DATA'),
  ('pytz/zoneinfo/Etc/Zulu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Zulu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bishkek',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bishkek',
   'DATA'),
  ('pytz/zoneinfo/Africa/Bamako',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Bamako',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lusaka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lusaka',
   'DATA'),
  ('pytz/zoneinfo/Europe/Gibraltar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Gibraltar',
   'DATA'),
  ('pytz/zoneinfo/PST8PDT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/PST8PDT',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kanton',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kanton',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pitcairn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pitcairn',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Kiritimati',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Kiritimati',
   'DATA'),
  ('pytz/zoneinfo/America/Danmarkshavn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Danmarkshavn',
   'DATA'),
  ('pytz/zoneinfo/UCT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/UCT',
   'DATA'),
  ('pytz/zoneinfo/Africa/Libreville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Libreville',
   'DATA'),
  ('pytz/zoneinfo/America/Curacao',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Curacao',
   'DATA'),
  ('pytz/zoneinfo/Etc/Universal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/Universal',
   'DATA'),
  ('pytz/zoneinfo/America/Martinique',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Martinique',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Galapagos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Galapagos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Srednekolymsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Srednekolymsk',
   'DATA'),
  ('pytz/zoneinfo/America/Nipigon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nipigon',
   'DATA'),
  ('pytz/zoneinfo/Brazil/East',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/East',
   'DATA'),
  ('pytz/zoneinfo/US/Alaska',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Alaska',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Norfolk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Norfolk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baghdad',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baghdad',
   'DATA'),
  ('pytz/zoneinfo/Africa/Dakar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Dakar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashgabat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashgabat',
   'DATA'),
  ('pytz/zoneinfo/GMT-0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Asia/Novokuznetsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Novokuznetsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Seoul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Seoul',
   'DATA'),
  ('pytz/zoneinfo/Africa/Sao_Tome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Sao_Tome',
   'DATA'),
  ('pytz/zoneinfo/America/Knox_IN',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Knox_IN',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Gambier',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Gambier',
   'DATA'),
  ('pytz/zoneinfo/America/Araguaina',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Araguaina',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tomsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tomsk',
   'DATA'),
  ('pytz/zoneinfo/Europe/Malta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Malta',
   'DATA'),
  ('pytz/zoneinfo/Australia/Yancowinna',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Yancowinna',
   'DATA'),
  ('pytz/zoneinfo/America/Phoenix',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Phoenix',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yerevan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yerevan',
   'DATA'),
  ('pytz/zoneinfo/Indian/Cocos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Cocos',
   'DATA'),
  ('pytz/zoneinfo/Europe/Rome',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Rome',
   'DATA'),
  ('pytz/zoneinfo/America/Dominica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Dominica',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/South_Georgia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/South_Georgia',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/San_Juan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/San_Juan',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Auckland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Auckland',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/South_Pole',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/South_Pole',
   'DATA'),
  ('pytz/zoneinfo/America/Nassau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Nassau',
   'DATA'),
  ('pytz/zoneinfo/America/St_Thomas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Thomas',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hong_Kong',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hong_Kong',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Noumea',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Noumea',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kiev',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kiev',
   'DATA'),
  ('pytz/zoneinfo/US/Central',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Central',
   'DATA'),
  ('pytz/zoneinfo/America/St_Barthelemy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Barthelemy',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimbu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimbu',
   'DATA'),
  ('pytz/zoneinfo/Canada/Eastern',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Eastern',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tashkent',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tashkent',
   'DATA'),
  ('pytz/zoneinfo/zone.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/zone.tab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Uzhgorod',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Uzhgorod',
   'DATA'),
  ('pytz/zoneinfo/America/St_Kitts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Kitts',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Niue',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Niue',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Cape_Verde',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Cape_Verde',
   'DATA'),
  ('pytz/zoneinfo/Africa/Juba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Juba',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Vostok',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Vostok',
   'DATA'),
  ('pytz/zoneinfo/MST',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/MST',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/St_Helena',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/St_Helena',
   'DATA'),
  ('pytz/zoneinfo/America/Puerto_Rico',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Puerto_Rico',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vientiane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vientiane',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-2',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Mendoza',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Mendoza',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belfast',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belfast',
   'DATA'),
  ('pytz/zoneinfo/America/Kentucky/Louisville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Kentucky/Louisville',
   'DATA'),
  ('pytz/zoneinfo/America/Belize',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Belize',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zurich',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zurich',
   'DATA'),
  ('pytz/zoneinfo/Australia/West',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/West',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Bermuda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Bermuda',
   'DATA'),
  ('pytz/zoneinfo/Australia/Lord_Howe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Lord_Howe',
   'DATA'),
  ('pytz/zoneinfo/Europe/Warsaw',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Warsaw',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+3',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+3',
   'DATA'),
  ('pytz/zoneinfo/Africa/Conakry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Conakry',
   'DATA'),
  ('pytz/zoneinfo/Iran',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Iran',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fiji',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fiji',
   'DATA'),
  ('pytz/zoneinfo/Canada/Yukon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Yukon',
   'DATA'),
  ('pytz/zoneinfo/America/Anguilla',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Anguilla',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuala_Lumpur',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuala_Lumpur',
   'DATA'),
  ('pytz/zoneinfo/America/Yakutat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Yakutat',
   'DATA'),
  ('pytz/zoneinfo/America/Recife',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Recife',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aden',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aden',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+12',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+12',
   'DATA'),
  ('pytz/zoneinfo/Africa/Nouakchott',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Nouakchott',
   'DATA'),
  ('pytz/zoneinfo/Europe/Tallinn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Tallinn',
   'DATA'),
  ('pytz/zoneinfo/UTC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/UTC',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT',
   'DATA'),
  ('pytz/zoneinfo/Eire',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Eire',
   'DATA'),
  ('pytz/zoneinfo/America/Porto_Acre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Porto_Acre',
   'DATA'),
  ('pytz/zoneinfo/America/Ensenada',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Ensenada',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Palmer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Palmer',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Efate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Efate',
   'DATA'),
  ('pytz/zoneinfo/Asia/Urumqi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Urumqi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Johannesburg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Johannesburg',
   'DATA'),
  ('pytz/zoneinfo/Europe/Prague',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Prague',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vatican',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vatican',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Rothera',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Rothera',
   'DATA'),
  ('pytz/zoneinfo/Kwajalein',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Kwajalein',
   'DATA'),
  ('pytz/zoneinfo/Europe/Budapest',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Budapest',
   'DATA'),
  ('pytz/zoneinfo/America/Paramaribo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Paramaribo',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Tell_City',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Tell_City',
   'DATA'),
  ('pytz/zoneinfo/Africa/Lagos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Lagos',
   'DATA'),
  ('pytz/zoneinfo/America/Maceio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Maceio',
   'DATA'),
  ('pytz/zoneinfo/Europe/Podgorica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Podgorica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Fakaofo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Fakaofo',
   'DATA'),
  ('pytz/zoneinfo/America/Chicago',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Chicago',
   'DATA'),
  ('pytz/zoneinfo/America/Yellowknife',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Yellowknife',
   'DATA'),
  ('pytz/zoneinfo/America/Buenos_Aires',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Buenos_Aires',
   'DATA'),
  ('pytz/zoneinfo/Europe/London',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/London',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+0',
   'DATA'),
  ('pytz/zoneinfo/America/Havana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Havana',
   'DATA'),
  ('pytz/zoneinfo/Indian/Reunion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Reunion',
   'DATA'),
  ('pytz/zoneinfo/tzdata.zi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/tzdata.zi',
   'DATA'),
  ('pytz/zoneinfo/Africa/Kinshasa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Kinshasa',
   'DATA'),
  ('pytz/zoneinfo/Asia/Yakutsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Yakutsk',
   'DATA'),
  ('pytz/zoneinfo/Asia/Damascus',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Damascus',
   'DATA'),
  ('pytz/zoneinfo/Europe/Brussels',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Brussels',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Truk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Truk',
   'DATA'),
  ('pytz/zoneinfo/US/Pacific',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Pacific',
   'DATA'),
  ('pytz/zoneinfo/Asia/Manila',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Manila',
   'DATA'),
  ('pytz/zoneinfo/US/Arizona',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Arizona',
   'DATA'),
  ('pytz/zoneinfo/US/Mountain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Africa/Timbuktu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Timbuktu',
   'DATA'),
  ('pytz/zoneinfo/America/Antigua',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Antigua',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Catamarca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Catamarca',
   'DATA'),
  ('pytz/zoneinfo/Asia/Rangoon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Rangoon',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtau',
   'DATA'),
  ('pytz/zoneinfo/America/Jujuy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/America/Glace_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Glace_Bay',
   'DATA'),
  ('pytz/zoneinfo/Factory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Factory',
   'DATA'),
  ('pytz/zoneinfo/Africa/Mogadishu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Mogadishu',
   'DATA'),
  ('pytz/zoneinfo/Australia/Adelaide',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Adelaide',
   'DATA'),
  ('pytz/zoneinfo/Indian/Kerguelen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Kerguelen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jakarta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jakarta',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Saipan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Saipan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-6',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-6',
   'DATA'),
  ('pytz/zoneinfo/America/Atikokan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Atikokan',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-14',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-14',
   'DATA'),
  ('pytz/zoneinfo/Asia/Chungking',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Chungking',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tel_Aviv',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tel_Aviv',
   'DATA'),
  ('pytz/zoneinfo/Europe/Istanbul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Asia/Taipei',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Taipei',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chatham',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chatham',
   'DATA'),
  ('pytz/zoneinfo/US/Indiana-Starke',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Indiana-Starke',
   'DATA'),
  ('pytz/zoneinfo/America/Coral_Harbour',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Coral_Harbour',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuching',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuching',
   'DATA'),
  ('pytz/zoneinfo/Europe/Bucharest',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Bucharest',
   'DATA'),
  ('pytz/zoneinfo/America/Aruba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Aruba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Nicosia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Nicosia',
   'DATA'),
  ('pytz/zoneinfo/Africa/Gaborone',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Gaborone',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dili',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dili',
   'DATA'),
  ('pytz/zoneinfo/Asia/Amman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Amman',
   'DATA'),
  ('pytz/zoneinfo/Europe/Paris',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Paris',
   'DATA'),
  ('pytz/zoneinfo/America/Thunder_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Thunder_Bay',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Troll',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Troll',
   'DATA'),
  ('pytz/zoneinfo/America/Pangnirtung',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Pangnirtung',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hebron',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hebron',
   'DATA'),
  ('pytz/zoneinfo/EET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/EET',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kamchatka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kamchatka',
   'DATA'),
  ('pytz/zoneinfo/Asia/Jayapura',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Jayapura',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kolkata',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kolkata',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/McMurdo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/McMurdo',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+11',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+11',
   'DATA'),
  ('pytz/zoneinfo/Europe/Simferopol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Simferopol',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Marengo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Marengo',
   'DATA'),
  ('pytz/zoneinfo/Etc/UTC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UTC',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ndjamena',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ndjamena',
   'DATA'),
  ('pytz/zoneinfo/America/Guadeloupe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Guadeloupe',
   'DATA'),
  ('pytz/zoneinfo/America/Marigot',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Marigot',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qyzylorda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qyzylorda',
   'DATA'),
  ('pytz/zoneinfo/America/Edmonton',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Edmonton',
   'DATA'),
  ('pytz/zoneinfo/America/Denver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Denver',
   'DATA'),
  ('pytz/zoneinfo/Indian/Chagos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Chagos',
   'DATA'),
  ('pytz/zoneinfo/Asia/Calcutta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Calcutta',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Davis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Davis',
   'DATA'),
  ('pytz/zoneinfo/America/Port-au-Prince',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Port-au-Prince',
   'DATA'),
  ('pytz/zoneinfo/Indian/Maldives',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Maldives',
   'DATA'),
  ('pytz/zoneinfo/Asia/Almaty',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Almaty',
   'DATA'),
  ('pytz/zoneinfo/America/Matamoros',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Matamoros',
   'DATA'),
  ('pytz/zoneinfo/America/Barbados',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Barbados',
   'DATA'),
  ('pytz/zoneinfo/Asia/Brunei',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Brunei',
   'DATA'),
  ('pytz/zoneinfo/Etc/UCT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/UCT',
   'DATA'),
  ('pytz/zoneinfo/Israel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Israel',
   'DATA'),
  ('pytz/zoneinfo/America/Shiprock',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Shiprock',
   'DATA'),
  ('pytz/zoneinfo/Africa/Ouagadougou',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Ouagadougou',
   'DATA'),
  ('pytz/zoneinfo/WET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/WET',
   'DATA'),
  ('pytz/zoneinfo/Cuba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Cuba',
   'DATA'),
  ('pytz/zoneinfo/Europe/Minsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Minsk',
   'DATA'),
  ('pytz/zoneinfo/America/Metlakatla',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Metlakatla',
   'DATA'),
  ('pytz/zoneinfo/GMT0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT0',
   'DATA'),
  ('pytz/zoneinfo/America/Whitehorse',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Whitehorse',
   'DATA'),
  ('pytz/zoneinfo/Japan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Japan',
   'DATA'),
  ('pytz/zoneinfo/Asia/Tokyo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Tokyo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Pago_Pago',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Pago_Pago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Bangkok',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Bangkok',
   'DATA'),
  ('pytz/zoneinfo/Asia/Irkutsk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Irkutsk',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Enderbury',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Enderbury',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+2',
   'DATA'),
  ('pytz/zoneinfo/Canada/Mountain',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Mountain',
   'DATA'),
  ('pytz/zoneinfo/Europe/Lisbon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Lisbon',
   'DATA'),
  ('pytz/zoneinfo/Portugal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Portugal',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Majuro',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Majuro',
   'DATA'),
  ('pytz/zoneinfo/America/Rainy_River',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Rainy_River',
   'DATA'),
  ('pytz/zoneinfo/NZ',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/NZ',
   'DATA'),
  ('pytz/zoneinfo/Poland',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Poland',
   'DATA'),
  ('pytz/zoneinfo/Asia/Harbin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Harbin',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tahiti',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tahiti',
   'DATA'),
  ('pytz/zoneinfo/Europe/Busingen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Busingen',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/ComodRivadavia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/ComodRivadavia',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kaliningrad',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kaliningrad',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Honolulu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Honolulu',
   'DATA'),
  ('pytz/zoneinfo/Europe/Chisinau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Chisinau',
   'DATA'),
  ('pytz/zoneinfo/US/Eastern',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Eastern',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/New_Salem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/New_Salem',
   'DATA'),
  ('pytz/zoneinfo/Europe/Kirov',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Kirov',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Tarawa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Tarawa',
   'DATA'),
  ('pytz/zoneinfo/Australia/Broken_Hill',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Broken_Hill',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-5',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-5',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ho_Chi_Minh',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ho_Chi_Minh',
   'DATA'),
  ('pytz/zoneinfo/Europe/Madrid',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Madrid',
   'DATA'),
  ('pytz/zoneinfo/GB',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GB',
   'DATA'),
  ('pytz/zoneinfo/America/Miquelon',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Miquelon',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-1',
   'DATA'),
  ('pytz/zoneinfo/Asia/Baku',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Baku',
   'DATA'),
  ('pytz/zoneinfo/Africa/Malabo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Malabo',
   'DATA'),
  ('pytz/zoneinfo/GB-Eire',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GB-Eire',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Rio_Gallegos',
   'DATA'),
  ('pytz/zoneinfo/America/Caracas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Caracas',
   'DATA'),
  ('pytz/zoneinfo/America/Costa_Rica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Costa_Rica',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Knox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Knox',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ashkhabad',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ashkhabad',
   'DATA'),
  ('pytz/zoneinfo/Indian/Antananarivo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Antananarivo',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Midway',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Midway',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Samoa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Samoa',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-9',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-9',
   'DATA'),
  ('pytz/zoneinfo/Asia/Thimphu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Thimphu',
   'DATA'),
  ('pytz/zoneinfo/Asia/Hovd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Hovd',
   'DATA'),
  ('pytz/zoneinfo/America/Dawson_Creek',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Dawson_Creek',
   'DATA'),
  ('pytz/zoneinfo/Asia/Karachi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Karachi',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Wake',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Wake',
   'DATA'),
  ('pytz/zoneinfo/Asia/Istanbul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Istanbul',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+5',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+5',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zaporozhye',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zaporozhye',
   'DATA'),
  ('pytz/zoneinfo/America/Los_Angeles',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Los_Angeles',
   'DATA'),
  ('pytz/zoneinfo/Africa/Abidjan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Abidjan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Astrakhan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Astrakhan',
   'DATA'),
  ('pytz/zoneinfo/Europe/Luxembourg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Luxembourg',
   'DATA'),
  ('pytz/zoneinfo/Australia/LHI',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/LHI',
   'DATA'),
  ('pytz/zoneinfo/Australia/Hobart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Hobart',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+7',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+7',
   'DATA'),
  ('pytz/zoneinfo/Asia/Makassar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Makassar',
   'DATA'),
  ('pytz/zoneinfo/Asia/Barnaul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Barnaul',
   'DATA'),
  ('pytz/zoneinfo/America/St_Vincent',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Vincent',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Mawson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Mawson',
   'DATA'),
  ('pytz/zoneinfo/America/Cuiaba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cuiaba',
   'DATA'),
  ('pytz/zoneinfo/Europe/San_Marino',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/San_Marino',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Stanley',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Stanley',
   'DATA'),
  ('pytz/zoneinfo/America/Tortola',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Tortola',
   'DATA'),
  ('pytz/zoneinfo/MET',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/MET',
   'DATA'),
  ('pytz/zoneinfo/America/North_Dakota/Beulah',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/North_Dakota/Beulah',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kuwait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kuwait',
   'DATA'),
  ('pytz/zoneinfo/Africa/Addis_Ababa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Addis_Ababa',
   'DATA'),
  ('pytz/zoneinfo/Europe/Guernsey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Guernsey',
   'DATA'),
  ('pytz/zoneinfo/US/Aleutian',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/Aleutian',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dacca',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dacca',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-7',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-7',
   'DATA'),
  ('pytz/zoneinfo/US/East-Indiana',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/US/East-Indiana',
   'DATA'),
  ('pytz/zoneinfo/America/Managua',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Managua',
   'DATA'),
  ('pytz/zoneinfo/America/Merida',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Merida',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Easter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Easter',
   'DATA'),
  ('pytz/zoneinfo/Asia/Oral',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Oral',
   'DATA'),
  ('pytz/zoneinfo/Europe/Isle_of_Man',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Isle_of_Man',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Madeira',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Madeira',
   'DATA'),
  ('pytz/zoneinfo/Asia/Vladivostok',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Vladivostok',
   'DATA'),
  ('pytz/zoneinfo/Africa/Cairo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Cairo',
   'DATA'),
  ('pytz/zoneinfo/Africa/Niamey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Niamey',
   'DATA'),
  ('pytz/zoneinfo/zonenow.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/zonenow.tab',
   'DATA'),
  ('pytz/zoneinfo/Africa/Accra',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Accra',
   'DATA'),
  ('pytz/zoneinfo/America/Vancouver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Vancouver',
   'DATA'),
  ('pytz/zoneinfo/America/Fort_Wayne',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Fort_Wayne',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kabul',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kabul',
   'DATA'),
  ('pytz/zoneinfo/EST',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/EST',
   'DATA'),
  ('pytz/zoneinfo/GMT',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/GMT',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Winamac',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Winamac',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Funafuti',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Funafuti',
   'DATA'),
  ('pytz/zoneinfo/Asia/Ujung_Pandang',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Ujung_Pandang',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Faroe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Faroe',
   'DATA'),
  ('pytz/zoneinfo/Australia/North',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/North',
   'DATA'),
  ('pytz/zoneinfo/Chile/Continental',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Chile/Continental',
   'DATA'),
  ('pytz/zoneinfo/Australia/Tasmania',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Tasmania',
   'DATA'),
  ('pytz/zoneinfo/Asia/Katmandu',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Katmandu',
   'DATA'),
  ('pytz/zoneinfo/America/Santiago',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santiago',
   'DATA'),
  ('pytz/zoneinfo/Asia/Qatar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Qatar',
   'DATA'),
  ('pytz/zoneinfo/America/Boa_Vista',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Boa_Vista',
   'DATA'),
  ('pytz/zoneinfo/Asia/Choibalsan',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Choibalsan',
   'DATA'),
  ('pytz/zoneinfo/Australia/Perth',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Perth',
   'DATA'),
  ('pytz/zoneinfo/Europe/Sofia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Sofia',
   'DATA'),
  ('pytz/zoneinfo/America/Godthab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Godthab',
   'DATA'),
  ('pytz/zoneinfo/Europe/Riga',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Riga',
   'DATA'),
  ('pytz/zoneinfo/America/Goose_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Goose_Bay',
   'DATA'),
  ('pytz/zoneinfo/Africa/Blantyre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Blantyre',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pontianak',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pontianak',
   'DATA'),
  ('pytz/zoneinfo/Asia/Macao',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Macao',
   'DATA'),
  ('pytz/zoneinfo/America/El_Salvador',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/El_Salvador',
   'DATA'),
  ('pytz/zoneinfo/America/Bogota',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Bogota',
   'DATA'),
  ('pytz/zoneinfo/Africa/Khartoum',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Khartoum',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Bougainville',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Bougainville',
   'DATA'),
  ('pytz/zoneinfo/Asia/Atyrau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Atyrau',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mauritius',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mauritius',
   'DATA'),
  ('pytz/zoneinfo/Asia/Beirut',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Beirut',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vienna',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vienna',
   'DATA'),
  ('pytz/zoneinfo/Asia/Anadyr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Anadyr',
   'DATA'),
  ('pytz/zoneinfo/Europe/Mariehamn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Mariehamn',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vaduz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vaduz',
   'DATA'),
  ('pytz/zoneinfo/America/Juneau',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Juneau',
   'DATA'),
  ('pytz/zoneinfo/America/Scoresbysund',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Scoresbysund',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-13',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-13',
   'DATA'),
  ('pytz/zoneinfo/Europe/Andorra',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Andorra',
   'DATA'),
  ('pytz/zoneinfo/zone1970.tab',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/zone1970.tab',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Azores',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Azores',
   'DATA'),
  ('pytz/zoneinfo/America/Belem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Belem',
   'DATA'),
  ('pytz/zoneinfo/Australia/Sydney',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Sydney',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-4',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-4',
   'DATA'),
  ('pytz/zoneinfo/Asia/Aqtobe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Aqtobe',
   'DATA'),
  ('pytz/zoneinfo/America/Cambridge_Bay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Cambridge_Bay',
   'DATA'),
  ('pytz/zoneinfo/ROC',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/ROC',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Guadalcanal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Guadalcanal',
   'DATA'),
  ('pytz/zoneinfo/HST',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/HST',
   'DATA'),
  ('pytz/zoneinfo/America/Indiana/Petersburg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indiana/Petersburg',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Chuuk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Chuuk',
   'DATA'),
  ('pytz/zoneinfo/ROK',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/ROK',
   'DATA'),
  ('pytz/zoneinfo/Europe/Jersey',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Jersey',
   'DATA'),
  ('pytz/zoneinfo/Europe/Belgrade',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Belgrade',
   'DATA'),
  ('pytz/zoneinfo/Europe/Copenhagen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Copenhagen',
   'DATA'),
  ('pytz/zoneinfo/America/Jamaica',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Jamaica',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Marquesas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Marquesas',
   'DATA'),
  ('pytz/zoneinfo/America/Atka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Atka',
   'DATA'),
  ('pytz/zoneinfo/Canada/Atlantic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Canada/Atlantic',
   'DATA'),
  ('pytz/zoneinfo/W-SU',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/W-SU',
   'DATA'),
  ('pytz/zoneinfo/America/Boise',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Boise',
   'DATA'),
  ('pytz/zoneinfo/Indian/Mayotte',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Indian/Mayotte',
   'DATA'),
  ('pytz/zoneinfo/Australia/Canberra',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Canberra',
   'DATA'),
  ('pytz/zoneinfo/Pacific/Apia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Pacific/Apia',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Jujuy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Jujuy',
   'DATA'),
  ('pytz/zoneinfo/Africa/Windhoek',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Windhoek',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Ushuaia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Ushuaia',
   'DATA'),
  ('pytz/zoneinfo/Mexico/BajaSur',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Mexico/BajaSur',
   'DATA'),
  ('pytz/zoneinfo/Australia/Brisbane',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Brisbane',
   'DATA'),
  ('pytz/zoneinfo/America/New_York',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/New_York',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-0',
   'DATA'),
  ('pytz/zoneinfo/Africa/Luanda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Luanda',
   'DATA'),
  ('pytz/zoneinfo/America/Manaus',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Manaus',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT+4',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT+4',
   'DATA'),
  ('pytz/zoneinfo/America/St_Johns',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/St_Johns',
   'DATA'),
  ('pytz/zoneinfo/America/Punta_Arenas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Punta_Arenas',
   'DATA'),
  ('pytz/zoneinfo/Europe/Zagreb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Zagreb',
   'DATA'),
  ('pytz/zoneinfo/America/Montserrat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Montserrat',
   'DATA'),
  ('pytz/zoneinfo/America/Indianapolis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Indianapolis',
   'DATA'),
  ('pytz/zoneinfo/America/Swift_Current',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Swift_Current',
   'DATA'),
  ('pytz/zoneinfo/Europe/Berlin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Berlin',
   'DATA'),
  ('pytz/zoneinfo/Europe/Amsterdam',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Amsterdam',
   'DATA'),
  ('pytz/zoneinfo/America/Bahia',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Bahia',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Jan_Mayen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Jan_Mayen',
   'DATA'),
  ('pytz/zoneinfo/Asia/Dhaka',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Dhaka',
   'DATA'),
  ('pytz/zoneinfo/Australia/Currie',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/Currie',
   'DATA'),
  ('pytz/zoneinfo/Africa/Porto-Novo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Porto-Novo',
   'DATA'),
  ('pytz/zoneinfo/America/Panama',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Panama',
   'DATA'),
  ('pytz/zoneinfo/Etc/GMT-11',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Etc/GMT-11',
   'DATA'),
  ('pytz/zoneinfo/Africa/Maseru',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Maseru',
   'DATA'),
  ('pytz/zoneinfo/Asia/Pyongyang',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Pyongyang',
   'DATA'),
  ('pytz/zoneinfo/Hongkong',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Hongkong',
   'DATA'),
  ('pytz/zoneinfo/Africa/Djibouti',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Africa/Djibouti',
   'DATA'),
  ('pytz/zoneinfo/Antarctica/Syowa',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Antarctica/Syowa',
   'DATA'),
  ('pytz/zoneinfo/America/Argentina/Cordoba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Argentina/Cordoba',
   'DATA'),
  ('pytz/zoneinfo/Asia/Kashgar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Asia/Kashgar',
   'DATA'),
  ('pytz/zoneinfo/Australia/NSW',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Australia/NSW',
   'DATA'),
  ('pytz/zoneinfo/Brazil/Acre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Brazil/Acre',
   'DATA'),
  ('pytz/zoneinfo/America/Santo_Domingo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Santo_Domingo',
   'DATA'),
  ('pytz/zoneinfo/Atlantic/Canary',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Atlantic/Canary',
   'DATA'),
  ('pytz/zoneinfo/America/Lima',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Lima',
   'DATA'),
  ('pytz/zoneinfo/America/Mexico_City',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Mexico_City',
   'DATA'),
  ('pytz/zoneinfo/America/Inuvik',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/America/Inuvik',
   'DATA'),
  ('pytz/zoneinfo/Europe/Vilnius',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/zoneinfo/Europe/Vilnius',
   'DATA'),
  ('pyarrow/include/arrow/testing/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/util/task_group.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/task_group.h',
   'DATA'),
  ('pyarrow/include/arrow/array.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array.h',
   'DATA'),
  ('pyarrow/include/arrow/table_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/table_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/pretty_print.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/pretty_print.h',
   'DATA'),
  ('pyarrow/include/arrow/util/small_vector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/small_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/options.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/test_in_memory_kms.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/test_in_memory_kms.h',
   'DATA'),
  ('pyarrow/include/arrow/array/diff.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/diff.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/parquet_encryption_config.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.cc',
   'DATA'),
  ('pyarrow/include/arrow/chunked_array.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/chunked_array.h',
   'DATA'),
  ('pyarrow/include/arrow/util/thread_pool.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/thread_pool.h',
   'DATA'),
  ('pyarrow/include/arrow/python/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/api.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/ieee.h',
   'DATA'),
  ('pyarrow/include/arrow/python/gdb.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/util/base64.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/base64.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_generate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_generate.h',
   'DATA'),
  ('pyarrow/include/arrow/json/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/ordering.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/ordering.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_binary.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_binary.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/visibility.h',
   'DATA'),
  ('pyarrow/includes/libparquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libparquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_run_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_run_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/python/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/path_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/path_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_test.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_tracing_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/io/slow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/slow.h',
   'DATA'),
  ('pyarrow/include/arrow/util/binary_view_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/binary_view_util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/aggregate_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/aggregate_node.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/plan.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/plan.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/test_util.h',
   'DATA'),
  ('pyarrow/includes/libarrow.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow.pxd',
   'DATA'),
  ('pyarrow/include/arrow/acero/map_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/map_node.h',
   'DATA'),
  ('pyarrow/includes/libarrow_substrait.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_aggregate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_aggregate.h',
   'DATA'),
  ('pyarrow/include/arrow/status.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/status.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/discovery.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/discovery.h',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.h',
   'DATA'),
  ('pyarrow/include/arrow/python/arrow_to_pandas.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/arrow_to_pandas.h',
   'DATA'),
  ('pyarrow/include/arrow/util/key_value_metadata.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/key_value_metadata.h',
   'DATA'),
  ('pyarrow/_azurefs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_azurefs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/relation.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/relation.h',
   'DATA'),
  ('pyarrow/include/arrow/util/ubsan.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/ubsan.h',
   'DATA'),
  ('pyarrow/lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/utf8.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/utf8.h',
   'DATA'),
  ('pyarrow/src/arrow/python/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/api.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/visibility.h',
   'DATA'),
  ('pyarrow/_csv.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_csv.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/crc32.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/crc32.h',
   'DATA'),
  ('pyarrow/include/arrow/python/python_test.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_test.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_array_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_array_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_writer.h',
   'DATA'),
  ('pyarrow/lib_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib_api.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_python_internal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client.h',
   'DATA'),
  ('pyarrow/include/arrow/util/time.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/time.h',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_convert.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/include/arrow/c/bridge.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/bridge.h',
   'DATA'),
  ('pyarrow/include/arrow/python/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_compatibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_compatibility.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/kernel.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/kernel.h',
   'DATA'),
  ('pyarrow/include/arrow/io/stdio.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/stdio.h',
   'DATA'),
  ('pyarrow/include/arrow/python/lib_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib_api.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/schema.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/schema.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/bloom_filter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/bloom_filter.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_base.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_base.h',
   'DATA'),
  ('pyarrow/include/arrow/config.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/config.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport_server.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport_server.h',
   'DATA'),
  ('pyarrow/include/arrow/util/basic_decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/basic_decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/stl_iterator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/stl_iterator.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_binary.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_binary.h',
   'DATA'),
  ('pyarrow/tests/data/orc/decimal.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/decimal.jsn.gz',
   'DATA'),
  ('pyarrow/include/arrow/util/hash_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/hash_util.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/expression.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/expression.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_set.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_set.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_writer.h',
   'DATA'),
  ('pyarrow/benchmark.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/benchmark.pxi',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool_test.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool_test.h',
   'DATA'),
  ('pyarrow/include/arrow/util/align_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/align_util.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/matchers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/matchers.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/type_fwd.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/utils.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/utils.h',
   'DATA'),
  ('pyarrow/_acero.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_acero.pyx',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_parquet.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_parquet.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_wrapper.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_wrapper.h',
   'DATA'),
  ('pyarrow/include/arrow/json/from_string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/from_string.h',
   'DATA'),
  ('pyarrow/include/parquet/level_conversion_inc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_conversion_inc.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/cached-powers.h',
   'DATA'),
  ('pyarrow/include/arrow/io/mman.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/mman.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_material_store.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_material_store.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/cast.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/cast.h',
   'DATA'),
  ('pyarrow/include/parquet/level_comparison_inc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_comparison_inc.h',
   'DATA'),
  ('pyarrow/_dataset_parquet_encryption.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_primitive.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_adaptive.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_adaptive.h',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/delimiting.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/delimiting.h',
   'DATA'),
  ('pyarrow/tests/extensions.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/extensions.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/ree_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/ree_util.h',
   'DATA'),
  ('pyarrow/__init__.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/__init__.pxd',
   'DATA'),
  ('pyarrow/include/arrow/dataset/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/api.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/include/arrow/python/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/test_nodes.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/test_nodes.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash/xxhash.h',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow/include/arrow/testing/executor_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/executor_util.h',
   'DATA'),
  ('pyarrow/gandiva.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/gandiva.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/opaque.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/opaque.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/engine/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/api.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_flight_server.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_flight_server.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/strtod.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_random.hpp',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/array/validate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/validate.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_plan_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/python/ipc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset.h',
   'DATA'),
  ('pyarrow/src/arrow/python/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/platform.h',
   'DATA'),
  ('pyarrow/src/arrow/python/ipc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/ipc.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/test_encryption_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/test_encryption_util.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow/include/parquet/encryption/kms_client_factory.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/kms_client_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_dict.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_dict.h',
   'DATA'),
  ('pyarrow/include/parquet/api/io.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/io.h',
   'DATA'),
  ('pyarrow/include/parquet/file_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/file_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/fixed_width_test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/fixed_width_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/options.h',
   'DATA'),
  ('pyarrow/include/parquet/stream_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/stream_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/filesystem_library.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/filesystem_library.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/query_context.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/query_context.h',
   'DATA'),
  ('pyarrow/include/parquet/column_scanner.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_scanner.h',
   'DATA'),
  ('pyarrow/include/arrow/util/functional.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/functional.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.cc',
   'DATA'),
  ('pyarrow/include/parquet/encryption/local_wrap_kms_client.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_run_end.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_run_end.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/hdfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/hdfs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/algorithm.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/algorithm.h',
   'DATA'),
  ('pyarrow/include/arrow/util/config.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/config.h',
   'DATA'),
  ('pyarrow/src/arrow/python/helpers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/api.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack_abi.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack_abi.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_orc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_orc.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/feather.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/feather.h',
   'DATA'),
  ('pyarrow/include/arrow/c/abi.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/abi.h',
   'DATA'),
  ('pyarrow/include/arrow/python/flight.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/include/parquet/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/type_fwd.h',
   'DATA'),
  ('pyarrow/_json.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_json.pyx',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow/include/parquet/column_page.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_page.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_tracing_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_tracing_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/middleware.h',
   'DATA'),
  ('pyarrow/include/parquet/xxhasher.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/xxhasher.h',
   'DATA'),
  ('pyarrow/include/arrow/util/vector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/vector.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_definitions.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_definitions.h',
   'DATA'),
  ('pyarrow/include/arrow/util/decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_time.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_time.h',
   'DATA'),
  ('pyarrow/include/arrow/util/endian.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/endian.h',
   'DATA'),
  ('pyarrow/include/arrow/record_batch.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/record_batch.h',
   'DATA'),
  ('pyarrow/include/parquet/api/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/order_by_impl.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/order_by_impl.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/localfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/localfs.h',
   'DATA'),
  ('pyarrow/include/arrow/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/iterators.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/include/parquet/column_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_reader.h',
   'DATA'),
  ('pyarrow/include/parquet/page_index.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/page_index.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/visibility.h',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/include/arrow/python/helpers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/helpers.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_auth.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_auth.h',
   'DATA'),
  ('pyarrow/include/parquet/bloom_filter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/bloom_filter.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_csv.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_csv.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/gdb.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/gdb.cc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/mockfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/mockfs.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor_generate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visitor_generate.h',
   'DATA'),
  ('pyarrow/include/arrow/visitor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visitor.h',
   'DATA'),
  ('pyarrow/include/arrow/util/union_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/union_util.h',
   'DATA'),
  ('pyarrow/include/arrow/memory_pool.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/memory_pool.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/interfaces.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/interfaces.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/transport.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/transport.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/strptime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/strptime.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_interop.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_union.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_union.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/random.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/random.h',
   'DATA'),
  ('pyarrow/include/arrow/python/decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/scanner.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/scanner.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/initialize.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/initialize.h',
   'DATA'),
  ('pyarrow/include/arrow/io/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/test_common.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow/include/arrow/util/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/type_traits.h',
   'DATA'),
  ('pyarrow/include/parquet/windows_compatibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/windows_compatibility.h',
   'DATA'),
  ('pyarrow/includes/libparquet_encryption.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/array.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/array.pxi',
   'DATA'),
  ('pyarrow/include/arrow/c/helpers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/helpers.h',
   'DATA'),
  ('pyarrow/_dataset.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset.pyx',
   'DATA'),
  ('pyarrow/_dlpack.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dlpack.pxi',
   'DATA'),
  ('pyarrow/include/parquet/benchmark_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/benchmark_util.h',
   'DATA'),
  ('pyarrow/pandas-shim.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/pandas-shim.pxi',
   'DATA'),
  ('pyarrow/include/arrow/acero/backpressure_handler.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/backpressure_handler.h',
   'DATA'),
  ('pyarrow/include/parquet/level_comparison.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_comparison.h',
   'DATA'),
  ('pyarrow/include/parquet/size_statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/size_statistics.h',
   'DATA'),
  ('pyarrow/includes/libarrow_python.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_python.pxd',
   'DATA'),
  ('pyarrow/_parquet_encryption.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_ops.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_ops.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum.h',
   'DATA'),
  ('pyarrow/include/arrow/array/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/util.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/util/byte_size.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/byte_size.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/api.h',
   'DATA'),
  ('pyarrow/include/arrow/result.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/result.h',
   'DATA'),
  ('pyarrow/include/arrow/json/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/api.h',
   'DATA'),
  ('pyarrow/_substrait.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_substrait.pyx',
   'DATA'),
  ('pyarrow/include/arrow/util/simd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/simd.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/type_fwd.h',
   'DATA'),
  ('pyarrow/_dataset_orc.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_orc.pyx',
   'DATA'),
  ('pyarrow/include/parquet/arrow/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/asof_join_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/asof_join_node.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/benchmark_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/benchmark_util.h',
   'DATA'),
  ('pyarrow/include/arrow/json/parser.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/parser.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_init.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_init.cc',
   'DATA'),
  ('pyarrow/tests/data/orc/decimal.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/decimal.orc',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/safe-math.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util.h',
   'DATA'),
  ('pyarrow/_feather.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_feather.pyx',
   'DATA'),
  ('pyarrow/include/arrow/scalar.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_data_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_data_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_dict.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_dict.h',
   'DATA'),
  ('pyarrow/_hdfs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_hdfs.pyx',
   'DATA'),
  ('pyarrow/include/parquet/types.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/types.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/inference.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.cc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3fs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3fs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.cc',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/regex.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/regex.h',
   'DATA'),
  ('pyarrow/tensor.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tensor.pxi',
   'DATA'),
  ('pyarrow/include/arrow/python/python_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/tests/pyarrow_cython_example.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow/include/parquet/arrow/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/extension_type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension_type.h',
   'DATA'),
  ('pyarrow/include/parquet/hasher.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/hasher.h',
   'DATA'),
  ('pyarrow/include/arrow/stl.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/stl.h',
   'DATA'),
  ('pyarrow/include/arrow/util/span.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/span.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/projector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/projector.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/accumulation_queue.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/accumulation_queue.h',
   'DATA'),
  ('pyarrow/src/arrow/python/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/udf.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/include/arrow/chunk_resolver.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/chunk_resolver.h',
   'DATA'),
  ('pyarrow/src/arrow/python/arrow_to_pandas.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/windows_fixup.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/windows_fixup.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_nested.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_nested.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.cc',
   'DATA'),
  ('pyarrow/include/parquet/statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/statistics.h',
   'DATA'),
  ('pyarrow/_dataset.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset.pxd',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_interop.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_interop.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/util/pcg_random.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/pcg_random.h',
   'DATA'),
  ('pyarrow/memory.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/memory.pxi',
   'DATA'),
  ('pyarrow/include/parquet/encryption/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/type_fwd.h',
   'DATA'),
  ('pyarrow/table.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/table.pxi',
   'DATA'),
  ('pyarrow/include/arrow/acero/tpch_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/tpch_node.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/util.h',
   'DATA'),
  ('pyarrow/include/arrow/datum.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/datum.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_auth.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_auth.h',
   'DATA'),
  ('pyarrow/include/arrow/util/async_generator_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/async_generator_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/python/iterators.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/iterators.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_internal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_internal.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_visit.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_visit.h',
   'DATA'),
  ('pyarrow/include/arrow/json/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/converter.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/crypto_factory.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/crypto_factory.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp',
   'DATA'),
  ('pyarrow/src/arrow/python/common.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.cc',
   'DATA'),
  ('pyarrow/src/arrow/python/flight.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/flight.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-to-string.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.test1.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow/include/parquet/printer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/printer.h',
   'DATA'),
  ('pyarrow/device.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/device.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_builders.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_builders.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.h',
   'DATA'),
  ('pyarrow/_orc.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_orc.pyx',
   'DATA'),
  ('pyarrow/include/arrow/io/buffered.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/buffered.h',
   'DATA'),
  ('pyarrow/includes/libarrow_acero.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_acero.pxd',
   'DATA'),
  ('pyarrow/include/arrow/io/hdfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/hdfs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/datetime.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/datetime.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/future.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/future.h',
   'DATA'),
  ('pyarrow/include/parquet/parquet_version.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/parquet_version.h',
   'DATA'),
  ('pyarrow/include/arrow/util/hashing.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/hashing.h',
   'DATA'),
  ('pyarrow/include/parquet/properties.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/properties.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/api.h',
   'DATA'),
  ('pyarrow/_compute.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute.pyx',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join.h',
   'DATA'),
  ('pyarrow/include/arrow/util/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/test_common.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/io/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/type_traits.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/serde.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/serde.h',
   'DATA'),
  ('pyarrow/includes/common.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/common.pxd',
   'DATA'),
  ('pyarrow/include/arrow/io/concurrency.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/concurrency.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/function_options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/function_options.h',
   'DATA'),
  ('pyarrow/scalar.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/scalar.pxi',
   'DATA'),
  ('pyarrow/src/arrow/python/async.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/async.h',
   'DATA'),
  ('pyarrow/tests/bound_function_visit_strings.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow/include/arrow/flight/types_async.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types_async.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/bool8.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/bool8.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_run_end.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_run_end.h',
   'DATA'),
  ('pyarrow/include/arrow/util/compression.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/compression.h',
   'DATA'),
  ('pyarrow/include/arrow/python/parquet_encryption.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/io.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/io.pxi',
   'DATA'),
  ('pyarrow/include/arrow/json/chunker.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunker.h',
   'DATA'),
  ('pyarrow/include/parquet/platform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/platform.h',
   'DATA'),
  ('pyarrow/include/arrow/util/int_util_overflow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/int_util_overflow.h',
   'DATA'),
  ('pyarrow/include/arrow/util/mutex.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/mutex.h',
   'DATA'),
  ('pyarrow/include/parquet/api/schema.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/schema.h',
   'DATA'),
  ('pyarrow/ipc.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/ipc.pxi',
   'DATA'),
  ('pyarrow/include/arrow/flight/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/visibility.h',
   'DATA'),
  ('pyarrow/tests/data/orc/README.md',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/README.md',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.cc',
   'DATA'),
  ('pyarrow/include/arrow/csv/reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/unreachable.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/unreachable.h',
   'DATA'),
  ('pyarrow/_parquet_encryption.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/options.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/chunker.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/chunker.h',
   'DATA'),
  ('pyarrow/include/arrow/util/list_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/list_util.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/s3_test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/s3_test_util.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_material.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_material.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/task_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/task_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/extension_type.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/extension_type.cc',
   'DATA'),
  ('pyarrow/include/arrow/csv/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/writer.h',
   'DATA'),
  ('pyarrow/include/parquet/file_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/file_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/builder.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/dictionary.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/dictionary.h',
   'DATA'),
  ('pyarrow/compat.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/compat.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_util.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/kms_client.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/kms_client.h',
   'DATA'),
  ('pyarrow/include/arrow/python/benchmark.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/includes/__init__.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/__init__.pxd',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.parquet',
   'DATA'),
  ('pyarrow/_cuda.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_cuda.pyx',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_scalar.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_scalar.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/tensorflow/convert.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/tensorflow/convert.h',
   'DATA'),
  ('pyarrow/lib.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.pyx',
   'DATA'),
  ('pyarrow/include/arrow/csv/parser.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/parser.h',
   'DATA'),
  ('pyarrow/includes/libarrow_feather.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_feather.pxd',
   'DATA'),
  ('pyarrow/include/arrow/buffer_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/buffer_builder.h',
   'DATA'),
  ('pyarrow/include/parquet/schema.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/schema.h',
   'DATA'),
  ('pyarrow/include/parquet/metadata.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/metadata.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/uniform_real.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/uniform_real.h',
   'DATA'),
  ('pyarrow/include/parquet/bloom_filter_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/bloom_filter_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/schema_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/schema_util.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/registry.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/registry.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_scalar_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_scalar_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/visit_type_inline.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/visit_type_inline.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/exec.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/exec.h',
   'DATA'),
  ('pyarrow/include/arrow/adapters/orc/adapter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/adapters/orc/adapter.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/util/math_constants.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/math_constants.h',
   'DATA'),
  ('pyarrow/include/arrow/python/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/string.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/async_test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/async_test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/python/filesystem.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_cookie_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_cookie_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/extension_types.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/extension_types.h',
   'DATA'),
  ('pyarrow/_json.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_json.pxd',
   'DATA'),
  ('pyarrow/_orc.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_orc.pxd',
   'DATA'),
  ('pyarrow/include/arrow/csv/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/options.h',
   'DATA'),
  ('pyarrow/include/arrow/json/chunked_builder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/chunked_builder.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap_reader.h',
   'DATA'),
  ('pyarrow/src/arrow/python/python_to_arrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/python_to_arrow.h',
   'DATA'),
  ('pyarrow/include/arrow/util/parallel.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/parallel.h',
   'DATA'),
  ('pyarrow/_flight.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_flight.pyx',
   'DATA'),
  ('pyarrow/include/arrow/io/transform.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/transform.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_util.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/column_decoder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/column_decoder.h',
   'DATA'),
  ('pyarrow/include/arrow/io/file.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/file.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.cc',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_json.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_json.h',
   'DATA'),
  ('pyarrow/include/arrow/type.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/type.h',
   'DATA'),
  ('pyarrow/include/arrow/util/concurrent_map.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/concurrent_map.h',
   'DATA'),
  ('pyarrow/include/arrow/util/benchmark_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/benchmark_util.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_base.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_base.h',
   'DATA'),
  ('pyarrow/include/arrow/device_allocation_type_set.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/device_allocation_type_set.h',
   'DATA'),
  ('pyarrow/include/parquet/exception.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/exception.h',
   'DATA'),
  ('pyarrow/include/arrow/util/launder.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/launder.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_toolkit.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_toolkit.h',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/gcsfs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/gcsfs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/type_traits.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/type_traits.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.h',
   'DATA'),
  ('pyarrow/include/arrow/json/object_parser.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/object_parser.h',
   'DATA'),
  ('pyarrow/include/arrow/python/datetime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/datetime.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/client_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/client_middleware.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cancel.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/cancel.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bit_block_counter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bit_block_counter.h',
   'DATA'),
  ('pyarrow/include/arrow/python/pyarrow_lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/pyarrow_lib.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/test_common.h',
   'DATA'),
  ('pyarrow/include/parquet/column_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/column_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/array/statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/statistics.h',
   'DATA'),
  ('pyarrow/builder.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/builder.pxi',
   'DATA'),
  ('pyarrow/includes/libarrow_cuda.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/testing/process.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/process.h',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.cc',
   'DATA'),
  ('pyarrow/include/parquet/level_conversion.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/level_conversion.h',
   'DATA'),
  ('pyarrow/_acero.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_acero.pxd',
   'DATA'),
  ('pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow/_csv.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_csv.pyx',
   'DATA'),
  ('pyarrow/include/parquet/stream_reader.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/stream_reader.h',
   'DATA'),
  ('pyarrow/include/arrow/util/io_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/io_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/common.h',
   'DATA'),
  ('pyarrow/src/arrow/python/inference.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/inference.h',
   'DATA'),
  ('pyarrow/include/arrow/io/compressed.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/compressed.h',
   'DATA'),
  ('pyarrow/include/arrow/json/rapidjson_defs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/rapidjson_defs.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/compute/row/grouper.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/row/grouper.h',
   'DATA'),
  ('pyarrow/src/arrow/python/decimal.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/decimal.cc',
   'DATA'),
  ('pyarrow/include/arrow/tensor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/uuid.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/uuid.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/diy-fp.h',
   'DATA'),
  ('pyarrow/include/parquet/encoding.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encoding.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/time_series_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/time_series_util.h',
   'DATA'),
  ('pyarrow/include/arrow/io/caching.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/caching.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h',
   'DATA'),
  ('pyarrow/_parquet.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/compute/api_vector.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compute/api_vector.h',
   'DATA'),
  ('pyarrow/include/arrow/json/test_common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/test_common.h',
   'DATA'),
  ('pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/vendored/pythoncapi_compat.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/udf.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/udf.cc',
   'DATA'),
  ('pyarrow/include/arrow/testing/generator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/generator.h',
   'DATA'),
  ('pyarrow/public-api.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/public-api.pxi',
   'DATA'),
  ('pyarrow/include/parquet/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/file_ipc.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/file_ipc.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logger.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/logger.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/test_auth_handlers.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/test_auth_handlers.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_metadata.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_metadata.h',
   'DATA'),
  ('pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/json/options.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/options.h',
   'DATA'),
  ('pyarrow/src/arrow/python/numpy_convert.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/numpy_convert.h',
   'DATA'),
  ('pyarrow/include/arrow/util/bitmap.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/bitmap.h',
   'DATA'),
  ('pyarrow/include/arrow/util/iterator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/iterator.h',
   'DATA'),
  ('pyarrow/include/arrow/extension/json.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/extension/json.h',
   'DATA'),
  ('pyarrow/includes/libarrow_dataset_parquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/python/lib.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/lib.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/string-to-double.h',
   'DATA'),
  ('pyarrow/include/arrow/csv/invalid_row.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/csv/invalid_row.h',
   'DATA'),
  ('pyarrow/include/arrow/util/value_parsing.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/value_parsing.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow_api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow_api.h',
   'DATA'),
  ('pyarrow/include/arrow/util/aligned_storage.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/aligned_storage.h',
   'DATA'),
  ('pyarrow/include/arrow/c/dlpack.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/c/dlpack.h',
   'DATA'),
  ('pyarrow/include/arrow/util/rows_to_batches.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/rows_to_batches.h',
   'DATA'),
  ('pyarrow/include/arrow/util/macros.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/macros.h',
   'DATA'),
  ('pyarrow/include/arrow/util/prefetch.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/prefetch.h',
   'DATA'),
  ('pyarrow/include/arrow/io/memory.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/memory.h',
   'DATA'),
  ('pyarrow/lib.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/lib.pxd',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz_private.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz_private.h',
   'DATA'),
  ('pyarrow/_pyarrow_cpp_tests.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow/include/arrow/stl_allocator.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/stl_allocator.h',
   'DATA'),
  ('pyarrow/src/arrow/python/pyarrow.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/pyarrow.cc',
   'DATA'),
  ('pyarrow/include/arrow/util/float16.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/float16.h',
   'DATA'),
  ('pyarrow/include/arrow/python/async.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/async.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/message.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/message.h',
   'DATA'),
  ('pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow/include/arrow/util/debug.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/debug.h',
   'DATA'),
  ('pyarrow/include/arrow/tensor/converter.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/tensor/converter.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/gtest_compat.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/gtest_compat.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_key_unwrapper.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_key_unwrapper.h',
   'DATA'),
  ('pyarrow/include/parquet/arrow/test_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/arrow/test_util.h',
   'DATA'),
  ('pyarrow/include/arrow/util/cpu_info.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/cpu_info.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp',
   'DATA'),
  ('pyarrow/_fs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_fs.pyx',
   'DATA'),
  ('pyarrow/_gcsfs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_gcsfs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/csv.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/csv.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/tz.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/tz.h',
   'DATA'),
  ('pyarrow/include/arrow/compare.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/compare.h',
   'DATA'),
  ('pyarrow/include/arrow/util/secure_string.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/secure_string.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/types.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/types.h',
   'DATA'),
  ('pyarrow/include/arrow/engine/substrait/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/engine/substrait/type_fwd.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-conversion.h',
   'DATA'),
  ('pyarrow/include/arrow/table.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/table.h',
   'DATA'),
  ('pyarrow/_dataset_parquet.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow/include/arrow/vendored/xxhash.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/xxhash.h',
   'DATA'),
  ('pyarrow/include/arrow/json/type_fwd.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/json/type_fwd.h',
   'DATA'),
  ('pyarrow/src/arrow/python/util.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/util.cc',
   'DATA'),
  ('pyarrow/includes/libgandiva.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libgandiva.pxd',
   'DATA'),
  ('pyarrow/include/arrow/array/concatenate.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/concatenate.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/exec_plan.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/exec_plan.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/ios.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/ios.h',
   'DATA'),
  ('pyarrow/includes/libarrow_flight.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_flight.pxd',
   'DATA'),
  ('pyarrow/include/arrow/testing/math.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/math.h',
   'DATA'),
  ('pyarrow/include/arrow/testing/future_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/testing/future_util.h',
   'DATA'),
  ('pyarrow/src/arrow/python/benchmark.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/benchmark.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server.h',
   'DATA'),
  ('pyarrow/include/arrow/util/range.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/range.h',
   'DATA'),
  ('pyarrow/includes/libarrow_fs.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/includes/libarrow_fs.pxd',
   'DATA'),
  ('pyarrow/_dataset_parquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow/include/arrow/util/queue.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/queue.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/encryption.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/device.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/device.h',
   'DATA'),
  ('pyarrow/src/arrow/python/parquet_encryption.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/parquet_encryption.h',
   'DATA'),
  ('pyarrow/include/arrow/util/checked_cast.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/checked_cast.h',
   'DATA'),
  ('pyarrow/include/arrow/util/formatting.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/formatting.h',
   'DATA'),
  ('pyarrow/include/arrow/util/compare.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/compare.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/server_middleware.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/server_middleware.h',
   'DATA'),
  ('pyarrow/_parquet.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_parquet.pxd',
   'DATA'),
  ('pyarrow/_fs.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_fs.pxd',
   'DATA'),
  ('pyarrow/src/arrow/python/CMakeLists.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/CMakeLists.txt',
   'DATA'),
  ('pyarrow/include/arrow/acero/partition_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/partition_util.h',
   'DATA'),
  ('pyarrow/include/parquet/geospatial/statistics.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/geospatial/statistics.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/file_system_key_material_store.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/file_system_key_material_store.h',
   'DATA'),
  ('pyarrow/error.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/error.pxi',
   'DATA'),
  ('pyarrow/include/arrow/python/numpy_init.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/numpy_init.h',
   'DATA'),
  ('pyarrow/src/arrow/python/io.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/io.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime.h',
   'DATA'),
  ('pyarrow/config.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/config.pxi',
   'DATA'),
  ('pyarrow/include/arrow/util/tracing.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/tracing.h',
   'DATA'),
  ('pyarrow/include/arrow/array/array_base.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/array_base.h',
   'DATA'),
  ('pyarrow/include/arrow/util/uri.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/uri.h',
   'DATA'),
  ('pyarrow/include/arrow/python/util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/util.h',
   'DATA'),
  ('pyarrow/include/arrow/acero/hash_join_node.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/acero/hash_join_node.h',
   'DATA'),
  ('pyarrow/include/arrow/sparse_tensor.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/sparse_tensor.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/api.h',
   'DATA'),
  ('pyarrow/include/parquet/encryption/key_encryption_key.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/encryption/key_encryption_key.h',
   'DATA'),
  ('pyarrow/include/parquet/windows_fixup.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/windows_fixup.h',
   'DATA'),
  ('pyarrow/include/arrow/ipc/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/ipc/writer.h',
   'DATA'),
  ('pyarrow/include/arrow/array/data.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/data.h',
   'DATA'),
  ('pyarrow/include/arrow/io/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/io/api.h',
   'DATA'),
  ('pyarrow/_cuda.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_cuda.pxd',
   'DATA'),
  ('pyarrow/include/arrow/dataset/partition.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/partition.h',
   'DATA'),
  ('pyarrow/include/arrow/python/io.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/io.h',
   'DATA'),
  ('pyarrow/_s3fs.pyx',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_s3fs.pyx',
   'DATA'),
  ('pyarrow/include/arrow/python/common.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/python/common.h',
   'DATA'),
  ('pyarrow/include/arrow/dataset/dataset_writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/dataset/dataset_writer.h',
   'DATA'),
  ('pyarrow/include/arrow/util/logging.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/logging.h',
   'DATA'),
  ('pyarrow/include/arrow/util/string_util.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/util/string_util.h',
   'DATA'),
  ('pyarrow/include/arrow/vendored/datetime/date.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/vendored/datetime/date.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_primitive.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_primitive.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_nested.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_nested.h',
   'DATA'),
  ('pyarrow/src/arrow/python/filesystem.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/filesystem.h',
   'DATA'),
  ('pyarrow/include/arrow/flight/otel_logging.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/flight/otel_logging.h',
   'DATA'),
  ('pyarrow/include/arrow/array/builder_dict.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/array/builder_dict.h',
   'DATA'),
  ('pyarrow/_compute.pxd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute.pxd',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/azurefs.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/azurefs.h',
   'DATA'),
  ('pyarrow/src/arrow/python/visibility.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/visibility.h',
   'DATA'),
  ('pyarrow/include/arrow/buffer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/buffer.h',
   'DATA'),
  ('pyarrow/types.pxi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/types.pxi',
   'DATA'),
  ('pyarrow/include/parquet/api/writer.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/parquet/api/writer.h',
   'DATA'),
  ('pyarrow/src/arrow/python/csv.cc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/src/arrow/python/csv.cc',
   'DATA'),
  ('pyarrow/include/arrow/filesystem/api.h',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/include/arrow/filesystem/api.h',
   'DATA'),
  ('altair/jupyter/js/index.js',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/js/index.js',
   'DATA'),
  ('altair/jupyter/js/README.md',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/js/README.md',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-lite-schema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-lite-schema.json',
   'DATA'),
  ('altair/py.typed',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/py.typed',
   'DATA'),
  ('altair/vegalite/v5/schema/vega-themes.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/vega-themes.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/content',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/meta-data',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/content',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/content',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/unevaluated',
   'DATA'),
  ('jsonschema_specifications/schemas/draft4/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft4/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft3/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft3/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-assertion',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/core',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/validation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft6/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft6/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/metaschema.json',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft201909/vocabularies/applicator',
   'DATA'),
  ('jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft202012/vocabularies/format-annotation',
   'DATA'),
  ('jsonschema_specifications/schemas/draft7/metaschema.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/schemas/draft7/metaschema.json',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/METADATA',
   'DATA'),
  ('jsonschema/benchmarks/issue232/issue.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/benchmarks/issue232/issue.json',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/RECORD',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/licenses/COPYING',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/licenses/COPYING',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/WHEEL',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/entry_points.txt',
   'DATA'),
  ('jsonschema-4.25.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema-4.25.0.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('numpy-2.3.2.dist-info/LICENSE.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/INSTALLER',
   'DATA'),
  ('numpy-2.3.2.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/RECORD',
   'DATA'),
  ('numpy-2.3.2.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('numpy-2.3.2.dist-info/entry_points.txt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/entry_points.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info/licenses/LICENSE',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs-25.3.0.dist-info/licenses/LICENSE',
   'DATA'),
  ('numpy-2.3.2.dist-info/WHEEL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy-2.3.2.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/base_library.zip',
   'DATA'),
  ('libarrow_acero.2100.dylib', 'pyarrow/libarrow_acero.2100.dylib', 'SYMLINK'),
  ('libparquet.2100.dylib', 'pyarrow/libparquet.2100.dylib', 'SYMLINK'),
  ('libarrow_dataset.2100.dylib',
   'pyarrow/libarrow_dataset.2100.dylib',
   'SYMLINK'),
  ('libarrow_compute.2100.dylib',
   'pyarrow/libarrow_compute.2100.dylib',
   'SYMLINK'),
  ('libarrow.2100.dylib', 'pyarrow/libarrow.2100.dylib', 'SYMLINK'),
  ('libarrow_substrait.2100.dylib',
   'pyarrow/libarrow_substrait.2100.dylib',
   'SYMLINK'),
  ('libarrow_flight.2100.dylib',
   'pyarrow/libarrow_flight.2100.dylib',
   'SYMLINK'),
  ('libarrow_python.2100.dylib',
   'pyarrow/libarrow_python.2100.dylib',
   'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libarrow_python_flight.2100.dylib',
   'pyarrow/libarrow_python_flight.2100.dylib',
   'SYMLINK'),
  ('libarrow_python_parquet_encryption.2100.dylib',
   'pyarrow/libarrow_python_parquet_encryption.2100.dylib',
   'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libavif.16.3.0.dylib', 'PIL/.dylibs/libavif.16.3.0.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libjpeg.62.4.0.dylib', 'PIL/.dylibs/libjpeg.62.4.0.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.3.dylib', 'PIL/.dylibs/libopenjp2.2.5.3.dylib', 'SYMLINK'),
  ('libz.1.3.1.zlib-ng.dylib',
   'PIL/.dylibs/libz.1.3.1.zlib-ng.dylib',
   'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libXau.6.dylib', 'PIL/.dylibs/libXau.6.dylib', 'SYMLINK')],
 [('encodings.zlib_codec',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/opt/miniconda3/envs/dist/lib/python3.13/encodings/__init__.py',
   'PYMODULE'),
  ('sre_compile',
   '/opt/miniconda3/envs/dist/lib/python3.13/sre_compile.py',
   'PYMODULE'),
  ('weakref',
   '/opt/miniconda3/envs/dist/lib/python3.13/weakref.py',
   'PYMODULE'),
  ('warnings',
   '/opt/miniconda3/envs/dist/lib/python3.13/warnings.py',
   'PYMODULE'),
  ('keyword',
   '/opt/miniconda3/envs/dist/lib/python3.13/keyword.py',
   'PYMODULE'),
  ('io', '/opt/miniconda3/envs/dist/lib/python3.13/io.py', 'PYMODULE'),
  ('genericpath',
   '/opt/miniconda3/envs/dist/lib/python3.13/genericpath.py',
   'PYMODULE'),
  ('functools',
   '/opt/miniconda3/envs/dist/lib/python3.13/functools.py',
   'PYMODULE'),
  ('enum', '/opt/miniconda3/envs/dist/lib/python3.13/enum.py', 'PYMODULE'),
  ('collections',
   '/opt/miniconda3/envs/dist/lib/python3.13/collections/__init__.py',
   'PYMODULE'),
  ('_weakrefset',
   '/opt/miniconda3/envs/dist/lib/python3.13/_weakrefset.py',
   'PYMODULE'),
  ('operator',
   '/opt/miniconda3/envs/dist/lib/python3.13/operator.py',
   'PYMODULE'),
  ('traceback',
   '/opt/miniconda3/envs/dist/lib/python3.13/traceback.py',
   'PYMODULE'),
  ('heapq', '/opt/miniconda3/envs/dist/lib/python3.13/heapq.py', 'PYMODULE'),
  ('stat', '/opt/miniconda3/envs/dist/lib/python3.13/stat.py', 'PYMODULE'),
  ('_collections_abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/_collections_abc.py',
   'PYMODULE'),
  ('types', '/opt/miniconda3/envs/dist/lib/python3.13/types.py', 'PYMODULE'),
  ('posixpath',
   '/opt/miniconda3/envs/dist/lib/python3.13/posixpath.py',
   'PYMODULE'),
  ('sre_constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/sre_constants.py',
   'PYMODULE'),
  ('re._parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/re/_parser.py',
   'PYMODULE'),
  ('re._constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/re/_constants.py',
   'PYMODULE'),
  ('re._compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/re/_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   '/opt/miniconda3/envs/dist/lib/python3.13/re/_casefix.py',
   'PYMODULE'),
  ('re', '/opt/miniconda3/envs/dist/lib/python3.13/re/__init__.py', 'PYMODULE'),
  ('linecache',
   '/opt/miniconda3/envs/dist/lib/python3.13/linecache.py',
   'PYMODULE'),
  ('copyreg',
   '/opt/miniconda3/envs/dist/lib/python3.13/copyreg.py',
   'PYMODULE'),
  ('abc', '/opt/miniconda3/envs/dist/lib/python3.13/abc.py', 'PYMODULE'),
  ('sre_parse',
   '/opt/miniconda3/envs/dist/lib/python3.13/sre_parse.py',
   'PYMODULE'),
  ('codecs', '/opt/miniconda3/envs/dist/lib/python3.13/codecs.py', 'PYMODULE'),
  ('locale', '/opt/miniconda3/envs/dist/lib/python3.13/locale.py', 'PYMODULE'),
  ('reprlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/reprlib.py',
   'PYMODULE'),
  ('ntpath', '/opt/miniconda3/envs/dist/lib/python3.13/ntpath.py', 'PYMODULE'),
  ('os', '/opt/miniconda3/envs/dist/lib/python3.13/os.py', 'PYMODULE')])
