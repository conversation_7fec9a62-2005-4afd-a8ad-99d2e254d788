('/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/build/app/PYZ-00.pyz',
 [('PIL',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('PIL._util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('PIL.features',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/PIL/features.py',
   'PYMODULE'),
  ('__future__',
   '/opt/miniconda3/envs/dist/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('_aix_support',
   '/opt/miniconda3/envs/dist/lib/python3.13/_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   '/opt/miniconda3/envs/dist/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/opt/miniconda3/envs/dist/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/opt/miniconda3/envs/dist/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('_ios_support',
   '/opt/miniconda3/envs/dist/lib/python3.13/_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/opt/miniconda3/envs/dist/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('_osx_support',
   '/opt/miniconda3/envs/dist/lib/python3.13/_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pyrepl/__init__.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   '/opt/miniconda3/envs/dist/lib/python3.13/_pyrepl/pager.py',
   'PYMODULE'),
  ('_strptime',
   '/opt/miniconda3/envs/dist/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/opt/miniconda3/envs/dist/lib/python3.13/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('_threading_local',
   '/opt/miniconda3/envs/dist/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('altair',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/__init__.py',
   'PYMODULE'),
  ('altair._magics',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/_magics.py',
   'PYMODULE'),
  ('altair.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/expr/__init__.py',
   'PYMODULE'),
  ('altair.expr.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/expr/core.py',
   'PYMODULE'),
  ('altair.jupyter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/__init__.py',
   'PYMODULE'),
  ('altair.jupyter.jupyter_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/jupyter/jupyter_chart.py',
   'PYMODULE'),
  ('altair.theme',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/theme.py',
   'PYMODULE'),
  ('altair.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/typing/__init__.py',
   'PYMODULE'),
  ('altair.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/__init__.py',
   'PYMODULE'),
  ('altair.utils._dfi_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_dfi_types.py',
   'PYMODULE'),
  ('altair.utils._importers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_importers.py',
   'PYMODULE'),
  ('altair.utils._show',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_show.py',
   'PYMODULE'),
  ('altair.utils._transformed_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_transformed_data.py',
   'PYMODULE'),
  ('altair.utils._vegafusion_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/_vegafusion_data.py',
   'PYMODULE'),
  ('altair.utils.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/compiler.py',
   'PYMODULE'),
  ('altair.utils.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/core.py',
   'PYMODULE'),
  ('altair.utils.data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/data.py',
   'PYMODULE'),
  ('altair.utils.deprecation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/deprecation.py',
   'PYMODULE'),
  ('altair.utils.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/display.py',
   'PYMODULE'),
  ('altair.utils.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/html.py',
   'PYMODULE'),
  ('altair.utils.mimebundle',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/mimebundle.py',
   'PYMODULE'),
  ('altair.utils.plugin_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/plugin_registry.py',
   'PYMODULE'),
  ('altair.utils.save',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/save.py',
   'PYMODULE'),
  ('altair.utils.schemapi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/schemapi.py',
   'PYMODULE'),
  ('altair.utils.selection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/selection.py',
   'PYMODULE'),
  ('altair.utils.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/utils/server.py',
   'PYMODULE'),
  ('altair.vegalite',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/__init__.py',
   'PYMODULE'),
  ('altair.vegalite.data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/data.py',
   'PYMODULE'),
  ('altair.vegalite.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/display.py',
   'PYMODULE'),
  ('altair.vegalite.v5',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/__init__.py',
   'PYMODULE'),
  ('altair.vegalite.v5.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/api.py',
   'PYMODULE'),
  ('altair.vegalite.v5.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/compiler.py',
   'PYMODULE'),
  ('altair.vegalite.v5.data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/data.py',
   'PYMODULE'),
  ('altair.vegalite.v5.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/display.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/__init__.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema._config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/_config.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/_typing.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema.channels',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/channels.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/core.py',
   'PYMODULE'),
  ('altair.vegalite.v5.schema.mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/schema/mixins.py',
   'PYMODULE'),
  ('altair.vegalite.v5.theme',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/altair/vegalite/v5/theme.py',
   'PYMODULE'),
  ('argparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('ast', '/opt/miniconda3/envs/dist/lib/python3.13/ast.py', 'PYMODULE'),
  ('asyncio',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/asyncio/windows_utils.py',
   'PYMODULE'),
  ('attr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_compat.py',
   'PYMODULE'),
  ('attr._config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_config.py',
   'PYMODULE'),
  ('attr._funcs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_funcs.py',
   'PYMODULE'),
  ('attr._make',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/filters.py',
   'PYMODULE'),
  ('attr.setters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/setters.py',
   'PYMODULE'),
  ('attr.validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attr/validators.py',
   'PYMODULE'),
  ('attrs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/filters.py',
   'PYMODULE'),
  ('attrs.setters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/setters.py',
   'PYMODULE'),
  ('attrs.validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/attrs/validators.py',
   'PYMODULE'),
  ('base64', '/opt/miniconda3/envs/dist/lib/python3.13/base64.py', 'PYMODULE'),
  ('bdb', '/opt/miniconda3/envs/dist/lib/python3.13/bdb.py', 'PYMODULE'),
  ('bisect', '/opt/miniconda3/envs/dist/lib/python3.13/bisect.py', 'PYMODULE'),
  ('blinker',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/blinker/__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/blinker/_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/blinker/base.py',
   'PYMODULE'),
  ('bz2', '/opt/miniconda3/envs/dist/lib/python3.13/bz2.py', 'PYMODULE'),
  ('cachetools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/__init__.py',
   'PYMODULE'),
  ('cachetools._cached',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/_cached.py',
   'PYMODULE'),
  ('cachetools._cachedmethod',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/_cachedmethod.py',
   'PYMODULE'),
  ('cachetools.keys',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/cachetools/keys.py',
   'PYMODULE'),
  ('calendar',
   '/opt/miniconda3/envs/dist/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('certifi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/certifi/core.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('click',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/__init__.py',
   'PYMODULE'),
  ('click._compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/_winconsole.py',
   'PYMODULE'),
  ('click.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/core.py',
   'PYMODULE'),
  ('click.decorators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/formatting.py',
   'PYMODULE'),
  ('click.globals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/globals.py',
   'PYMODULE'),
  ('click.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/termui.py',
   'PYMODULE'),
  ('click.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/types.py',
   'PYMODULE'),
  ('click.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/click/utils.py',
   'PYMODULE'),
  ('cmd', '/opt/miniconda3/envs/dist/lib/python3.13/cmd.py', 'PYMODULE'),
  ('code', '/opt/miniconda3/envs/dist/lib/python3.13/code.py', 'PYMODULE'),
  ('codeop', '/opt/miniconda3/envs/dist/lib/python3.13/codeop.py', 'PYMODULE'),
  ('colorsys',
   '/opt/miniconda3/envs/dist/lib/python3.13/colorsys.py',
   'PYMODULE'),
  ('concurrent',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/opt/miniconda3/envs/dist/lib/python3.13/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/opt/miniconda3/envs/dist/lib/python3.13/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/opt/miniconda3/envs/dist/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('copy', '/opt/miniconda3/envs/dist/lib/python3.13/copy.py', 'PYMODULE'),
  ('csv', '/opt/miniconda3/envs/dist/lib/python3.13/csv.py', 'PYMODULE'),
  ('ctypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('ctypes.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/ctypes/wintypes.py',
   'PYMODULE'),
  ('curses',
   '/opt/miniconda3/envs/dist/lib/python3.13/curses/__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   '/opt/miniconda3/envs/dist/lib/python3.13/curses/has_key.py',
   'PYMODULE'),
  ('dataclasses',
   '/opt/miniconda3/envs/dist/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/opt/miniconda3/envs/dist/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('dateutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('decimal',
   '/opt/miniconda3/envs/dist/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('difflib',
   '/opt/miniconda3/envs/dist/lib/python3.13/difflib.py',
   'PYMODULE'),
  ('dis', '/opt/miniconda3/envs/dist/lib/python3.13/dis.py', 'PYMODULE'),
  ('doctest',
   '/opt/miniconda3/envs/dist/lib/python3.13/doctest.py',
   'PYMODULE'),
  ('email',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('fileinput',
   '/opt/miniconda3/envs/dist/lib/python3.13/fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   '/opt/miniconda3/envs/dist/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/opt/miniconda3/envs/dist/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('ftplib', '/opt/miniconda3/envs/dist/lib/python3.13/ftplib.py', 'PYMODULE'),
  ('getopt', '/opt/miniconda3/envs/dist/lib/python3.13/getopt.py', 'PYMODULE'),
  ('getpass',
   '/opt/miniconda3/envs/dist/lib/python3.13/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/opt/miniconda3/envs/dist/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('git',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/__init__.py',
   'PYMODULE'),
  ('git.cmd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/cmd.py',
   'PYMODULE'),
  ('git.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/compat.py',
   'PYMODULE'),
  ('git.config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/config.py',
   'PYMODULE'),
  ('git.db',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/db.py',
   'PYMODULE'),
  ('git.diff',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/diff.py',
   'PYMODULE'),
  ('git.exc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/exc.py',
   'PYMODULE'),
  ('git.index',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/__init__.py',
   'PYMODULE'),
  ('git.index.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/base.py',
   'PYMODULE'),
  ('git.index.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/fun.py',
   'PYMODULE'),
  ('git.index.typ',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/typ.py',
   'PYMODULE'),
  ('git.index.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/index/util.py',
   'PYMODULE'),
  ('git.objects',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/__init__.py',
   'PYMODULE'),
  ('git.objects.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/base.py',
   'PYMODULE'),
  ('git.objects.blob',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/blob.py',
   'PYMODULE'),
  ('git.objects.commit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/commit.py',
   'PYMODULE'),
  ('git.objects.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/fun.py',
   'PYMODULE'),
  ('git.objects.submodule',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/__init__.py',
   'PYMODULE'),
  ('git.objects.submodule.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/base.py',
   'PYMODULE'),
  ('git.objects.submodule.root',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/root.py',
   'PYMODULE'),
  ('git.objects.submodule.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/submodule/util.py',
   'PYMODULE'),
  ('git.objects.tag',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/tag.py',
   'PYMODULE'),
  ('git.objects.tree',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/tree.py',
   'PYMODULE'),
  ('git.objects.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/objects/util.py',
   'PYMODULE'),
  ('git.refs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/__init__.py',
   'PYMODULE'),
  ('git.refs.head',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/head.py',
   'PYMODULE'),
  ('git.refs.log',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/log.py',
   'PYMODULE'),
  ('git.refs.reference',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/reference.py',
   'PYMODULE'),
  ('git.refs.remote',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/remote.py',
   'PYMODULE'),
  ('git.refs.symbolic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/symbolic.py',
   'PYMODULE'),
  ('git.refs.tag',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/refs/tag.py',
   'PYMODULE'),
  ('git.remote',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/remote.py',
   'PYMODULE'),
  ('git.repo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/repo/__init__.py',
   'PYMODULE'),
  ('git.repo.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/repo/base.py',
   'PYMODULE'),
  ('git.repo.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/repo/fun.py',
   'PYMODULE'),
  ('git.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/types.py',
   'PYMODULE'),
  ('git.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/git/util.py',
   'PYMODULE'),
  ('gitdb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/__init__.py',
   'PYMODULE'),
  ('gitdb.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/base.py',
   'PYMODULE'),
  ('gitdb.const',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/const.py',
   'PYMODULE'),
  ('gitdb.db',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/__init__.py',
   'PYMODULE'),
  ('gitdb.db.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/base.py',
   'PYMODULE'),
  ('gitdb.db.git',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/git.py',
   'PYMODULE'),
  ('gitdb.db.loose',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/loose.py',
   'PYMODULE'),
  ('gitdb.db.mem',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/mem.py',
   'PYMODULE'),
  ('gitdb.db.pack',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/pack.py',
   'PYMODULE'),
  ('gitdb.db.ref',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/db/ref.py',
   'PYMODULE'),
  ('gitdb.exc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/exc.py',
   'PYMODULE'),
  ('gitdb.fun',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/fun.py',
   'PYMODULE'),
  ('gitdb.pack',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/pack.py',
   'PYMODULE'),
  ('gitdb.stream',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/stream.py',
   'PYMODULE'),
  ('gitdb.typ',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/typ.py',
   'PYMODULE'),
  ('gitdb.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/util.py',
   'PYMODULE'),
  ('gitdb.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/utils/__init__.py',
   'PYMODULE'),
  ('gitdb.utils.encoding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/gitdb/utils/encoding.py',
   'PYMODULE'),
  ('glob', '/opt/miniconda3/envs/dist/lib/python3.13/glob.py', 'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google._upb', '-', 'PYMODULE'),
  ('google.protobuf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/__init__.py',
   'PYMODULE'),
  ('google.protobuf.any_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/any_pb2.py',
   'PYMODULE'),
  ('google.protobuf.api_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/api_pb2.py',
   'PYMODULE'),
  ('google.protobuf.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/compiler/__init__.py',
   'PYMODULE'),
  ('google.protobuf.compiler.plugin_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/compiler/plugin_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_database',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor_database.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pool',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/descriptor_pool.py',
   'PYMODULE'),
  ('google.protobuf.duration_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/duration_pb2.py',
   'PYMODULE'),
  ('google.protobuf.empty_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/empty_pb2.py',
   'PYMODULE'),
  ('google.protobuf.field_mask_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/field_mask_pb2.py',
   'PYMODULE'),
  ('google.protobuf.internal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/__init__.py',
   'PYMODULE'),
  ('google.protobuf.internal.api_implementation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/api_implementation.py',
   'PYMODULE'),
  ('google.protobuf.internal.builder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/builder.py',
   'PYMODULE'),
  ('google.protobuf.internal.containers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/containers.py',
   'PYMODULE'),
  ('google.protobuf.internal.decoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/decoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.encoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/encoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.enum_type_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/enum_type_wrapper.py',
   'PYMODULE'),
  ('google.protobuf.internal.extension_dict',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/extension_dict.py',
   'PYMODULE'),
  ('google.protobuf.internal.field_mask',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/field_mask.py',
   'PYMODULE'),
  ('google.protobuf.internal.message_listener',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/message_listener.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_edition_defaults',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/python_edition_defaults.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_message',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/python_message.py',
   'PYMODULE'),
  ('google.protobuf.internal.type_checkers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/type_checkers.py',
   'PYMODULE'),
  ('google.protobuf.internal.well_known_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/well_known_types.py',
   'PYMODULE'),
  ('google.protobuf.internal.wire_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/internal/wire_format.py',
   'PYMODULE'),
  ('google.protobuf.json_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/json_format.py',
   'PYMODULE'),
  ('google.protobuf.message',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/message.py',
   'PYMODULE'),
  ('google.protobuf.message_factory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/message_factory.py',
   'PYMODULE'),
  ('google.protobuf.proto_builder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/proto_builder.py',
   'PYMODULE'),
  ('google.protobuf.pyext',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/pyext/__init__.py',
   'PYMODULE'),
  ('google.protobuf.pyext.cpp_message',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/pyext/cpp_message.py',
   'PYMODULE'),
  ('google.protobuf.reflection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/reflection.py',
   'PYMODULE'),
  ('google.protobuf.runtime_version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/runtime_version.py',
   'PYMODULE'),
  ('google.protobuf.service_reflection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/service_reflection.py',
   'PYMODULE'),
  ('google.protobuf.source_context_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/source_context_pb2.py',
   'PYMODULE'),
  ('google.protobuf.struct_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/struct_pb2.py',
   'PYMODULE'),
  ('google.protobuf.symbol_database',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/symbol_database.py',
   'PYMODULE'),
  ('google.protobuf.text_encoding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/text_encoding.py',
   'PYMODULE'),
  ('google.protobuf.text_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/text_format.py',
   'PYMODULE'),
  ('google.protobuf.timestamp_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/timestamp_pb2.py',
   'PYMODULE'),
  ('google.protobuf.type_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/type_pb2.py',
   'PYMODULE'),
  ('google.protobuf.unknown_fields',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/unknown_fields.py',
   'PYMODULE'),
  ('google.protobuf.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/util/__init__.py',
   'PYMODULE'),
  ('google.protobuf.wrappers_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/google/protobuf/wrappers_pb2.py',
   'PYMODULE'),
  ('gzip', '/opt/miniconda3/envs/dist/lib/python3.13/gzip.py', 'PYMODULE'),
  ('hashlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('hmac', '/opt/miniconda3/envs/dist/lib/python3.13/hmac.py', 'PYMODULE'),
  ('html',
   '/opt/miniconda3/envs/dist/lib/python3.13/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/opt/miniconda3/envs/dist/lib/python3.13/html/entities.py',
   'PYMODULE'),
  ('http',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/cookies.py',
   'PYMODULE'),
  ('http.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/http/server.py',
   'PYMODULE'),
  ('idna',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('importlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/opt/miniconda3/envs/dist/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('jinja2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jinja2/visitor.py',
   'PYMODULE'),
  ('json',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/opt/miniconda3/envs/dist/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('jsonschema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/__init__.py',
   'PYMODULE'),
  ('jsonschema._format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_format.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_keywords.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_types.py',
   'PYMODULE'),
  ('jsonschema._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_typing.py',
   'PYMODULE'),
  ('jsonschema._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/_utils.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/exceptions.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/protocols.py',
   'PYMODULE'),
  ('jsonschema.validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema/validators.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/jsonschema_specifications/_core.py',
   'PYMODULE'),
  ('logging',
   '/opt/miniconda3/envs/dist/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   '/opt/miniconda3/envs/dist/lib/python3.13/logging/handlers.py',
   'PYMODULE'),
  ('lzma', '/opt/miniconda3/envs/dist/lib/python3.13/lzma.py', 'PYMODULE'),
  ('markupsafe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/markupsafe/__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/markupsafe/_native.py',
   'PYMODULE'),
  ('mimetypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/multiprocessing/util.py',
   'PYMODULE'),
  ('narwhals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/__init__.py',
   'PYMODULE'),
  ('narwhals._arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/__init__.py',
   'PYMODULE'),
  ('narwhals._arrow.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/dataframe.py',
   'PYMODULE'),
  ('narwhals._arrow.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/expr.py',
   'PYMODULE'),
  ('narwhals._arrow.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/group_by.py',
   'PYMODULE'),
  ('narwhals._arrow.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/namespace.py',
   'PYMODULE'),
  ('narwhals._arrow.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/selectors.py',
   'PYMODULE'),
  ('narwhals._arrow.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series.py',
   'PYMODULE'),
  ('narwhals._arrow.series_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_cat.py',
   'PYMODULE'),
  ('narwhals._arrow.series_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_dt.py',
   'PYMODULE'),
  ('narwhals._arrow.series_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_list.py',
   'PYMODULE'),
  ('narwhals._arrow.series_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_str.py',
   'PYMODULE'),
  ('narwhals._arrow.series_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/series_struct.py',
   'PYMODULE'),
  ('narwhals._arrow.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/typing.py',
   'PYMODULE'),
  ('narwhals._arrow.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_arrow/utils.py',
   'PYMODULE'),
  ('narwhals._compliant',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/__init__.py',
   'PYMODULE'),
  ('narwhals._compliant.any_namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/any_namespace.py',
   'PYMODULE'),
  ('narwhals._compliant.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/dataframe.py',
   'PYMODULE'),
  ('narwhals._compliant.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/expr.py',
   'PYMODULE'),
  ('narwhals._compliant.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/group_by.py',
   'PYMODULE'),
  ('narwhals._compliant.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/namespace.py',
   'PYMODULE'),
  ('narwhals._compliant.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/selectors.py',
   'PYMODULE'),
  ('narwhals._compliant.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/series.py',
   'PYMODULE'),
  ('narwhals._compliant.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/typing.py',
   'PYMODULE'),
  ('narwhals._compliant.when_then',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/when_then.py',
   'PYMODULE'),
  ('narwhals._compliant.window',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_compliant/window.py',
   'PYMODULE'),
  ('narwhals._constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_constants.py',
   'PYMODULE'),
  ('narwhals._dask',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/__init__.py',
   'PYMODULE'),
  ('narwhals._dask.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/dataframe.py',
   'PYMODULE'),
  ('narwhals._dask.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/expr.py',
   'PYMODULE'),
  ('narwhals._dask.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/expr_dt.py',
   'PYMODULE'),
  ('narwhals._dask.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/expr_str.py',
   'PYMODULE'),
  ('narwhals._dask.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/group_by.py',
   'PYMODULE'),
  ('narwhals._dask.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/namespace.py',
   'PYMODULE'),
  ('narwhals._dask.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/selectors.py',
   'PYMODULE'),
  ('narwhals._dask.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_dask/utils.py',
   'PYMODULE'),
  ('narwhals._duckdb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/__init__.py',
   'PYMODULE'),
  ('narwhals._duckdb.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/dataframe.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_dt.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_list.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_str.py',
   'PYMODULE'),
  ('narwhals._duckdb.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/expr_struct.py',
   'PYMODULE'),
  ('narwhals._duckdb.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/group_by.py',
   'PYMODULE'),
  ('narwhals._duckdb.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/namespace.py',
   'PYMODULE'),
  ('narwhals._duckdb.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/selectors.py',
   'PYMODULE'),
  ('narwhals._duckdb.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/series.py',
   'PYMODULE'),
  ('narwhals._duckdb.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duckdb/utils.py',
   'PYMODULE'),
  ('narwhals._duration',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_duration.py',
   'PYMODULE'),
  ('narwhals._enum',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_enum.py',
   'PYMODULE'),
  ('narwhals._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_exceptions.py',
   'PYMODULE'),
  ('narwhals._expression_parsing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_expression_parsing.py',
   'PYMODULE'),
  ('narwhals._ibis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/__init__.py',
   'PYMODULE'),
  ('narwhals._ibis.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/dataframe.py',
   'PYMODULE'),
  ('narwhals._ibis.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_dt.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_list.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_str.py',
   'PYMODULE'),
  ('narwhals._ibis.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/expr_struct.py',
   'PYMODULE'),
  ('narwhals._ibis.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/group_by.py',
   'PYMODULE'),
  ('narwhals._ibis.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/namespace.py',
   'PYMODULE'),
  ('narwhals._ibis.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/selectors.py',
   'PYMODULE'),
  ('narwhals._ibis.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/series.py',
   'PYMODULE'),
  ('narwhals._ibis.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_ibis/utils.py',
   'PYMODULE'),
  ('narwhals._interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_interchange/__init__.py',
   'PYMODULE'),
  ('narwhals._interchange.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_interchange/dataframe.py',
   'PYMODULE'),
  ('narwhals._interchange.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_interchange/series.py',
   'PYMODULE'),
  ('narwhals._namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_namespace.py',
   'PYMODULE'),
  ('narwhals._pandas_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/__init__.py',
   'PYMODULE'),
  ('narwhals._pandas_like.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/dataframe.py',
   'PYMODULE'),
  ('narwhals._pandas_like.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/expr.py',
   'PYMODULE'),
  ('narwhals._pandas_like.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/group_by.py',
   'PYMODULE'),
  ('narwhals._pandas_like.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/namespace.py',
   'PYMODULE'),
  ('narwhals._pandas_like.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/selectors.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_cat.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_dt.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_list.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_str.py',
   'PYMODULE'),
  ('narwhals._pandas_like.series_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/series_struct.py',
   'PYMODULE'),
  ('narwhals._pandas_like.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/typing.py',
   'PYMODULE'),
  ('narwhals._pandas_like.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_pandas_like/utils.py',
   'PYMODULE'),
  ('narwhals._polars',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/__init__.py',
   'PYMODULE'),
  ('narwhals._polars.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/dataframe.py',
   'PYMODULE'),
  ('narwhals._polars.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/expr.py',
   'PYMODULE'),
  ('narwhals._polars.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/group_by.py',
   'PYMODULE'),
  ('narwhals._polars.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/namespace.py',
   'PYMODULE'),
  ('narwhals._polars.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/series.py',
   'PYMODULE'),
  ('narwhals._polars.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/typing.py',
   'PYMODULE'),
  ('narwhals._polars.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_polars/utils.py',
   'PYMODULE'),
  ('narwhals._spark_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/__init__.py',
   'PYMODULE'),
  ('narwhals._spark_like.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/dataframe.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_dt.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_list.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_str.py',
   'PYMODULE'),
  ('narwhals._spark_like.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/expr_struct.py',
   'PYMODULE'),
  ('narwhals._spark_like.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/group_by.py',
   'PYMODULE'),
  ('narwhals._spark_like.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/namespace.py',
   'PYMODULE'),
  ('narwhals._spark_like.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/selectors.py',
   'PYMODULE'),
  ('narwhals._spark_like.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_spark_like/utils.py',
   'PYMODULE'),
  ('narwhals._sql',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/__init__.py',
   'PYMODULE'),
  ('narwhals._sql.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/dataframe.py',
   'PYMODULE'),
  ('narwhals._sql.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/expr.py',
   'PYMODULE'),
  ('narwhals._sql.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/group_by.py',
   'PYMODULE'),
  ('narwhals._sql.namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/namespace.py',
   'PYMODULE'),
  ('narwhals._sql.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/typing.py',
   'PYMODULE'),
  ('narwhals._sql.when_then',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_sql/when_then.py',
   'PYMODULE'),
  ('narwhals._translate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_translate.py',
   'PYMODULE'),
  ('narwhals._typing_compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_typing_compat.py',
   'PYMODULE'),
  ('narwhals._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/_utils.py',
   'PYMODULE'),
  ('narwhals.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/dataframe.py',
   'PYMODULE'),
  ('narwhals.dependencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/dependencies.py',
   'PYMODULE'),
  ('narwhals.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/dtypes.py',
   'PYMODULE'),
  ('narwhals.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/exceptions.py',
   'PYMODULE'),
  ('narwhals.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr.py',
   'PYMODULE'),
  ('narwhals.expr_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_cat.py',
   'PYMODULE'),
  ('narwhals.expr_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_dt.py',
   'PYMODULE'),
  ('narwhals.expr_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_list.py',
   'PYMODULE'),
  ('narwhals.expr_name',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_name.py',
   'PYMODULE'),
  ('narwhals.expr_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_str.py',
   'PYMODULE'),
  ('narwhals.expr_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/expr_struct.py',
   'PYMODULE'),
  ('narwhals.functions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/functions.py',
   'PYMODULE'),
  ('narwhals.group_by',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/group_by.py',
   'PYMODULE'),
  ('narwhals.schema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/schema.py',
   'PYMODULE'),
  ('narwhals.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/selectors.py',
   'PYMODULE'),
  ('narwhals.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series.py',
   'PYMODULE'),
  ('narwhals.series_cat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_cat.py',
   'PYMODULE'),
  ('narwhals.series_dt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_dt.py',
   'PYMODULE'),
  ('narwhals.series_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_list.py',
   'PYMODULE'),
  ('narwhals.series_str',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_str.py',
   'PYMODULE'),
  ('narwhals.series_struct',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/series_struct.py',
   'PYMODULE'),
  ('narwhals.stable',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/__init__.py',
   'PYMODULE'),
  ('narwhals.stable.v1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/__init__.py',
   'PYMODULE'),
  ('narwhals.stable.v1._dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/_dtypes.py',
   'PYMODULE'),
  ('narwhals.stable.v1._namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/_namespace.py',
   'PYMODULE'),
  ('narwhals.stable.v1.dependencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/dependencies.py',
   'PYMODULE'),
  ('narwhals.stable.v1.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/dtypes.py',
   'PYMODULE'),
  ('narwhals.stable.v1.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/selectors.py',
   'PYMODULE'),
  ('narwhals.stable.v1.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v1/typing.py',
   'PYMODULE'),
  ('narwhals.stable.v2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/__init__.py',
   'PYMODULE'),
  ('narwhals.stable.v2._namespace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/_namespace.py',
   'PYMODULE'),
  ('narwhals.stable.v2.dependencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/dependencies.py',
   'PYMODULE'),
  ('narwhals.stable.v2.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/dtypes.py',
   'PYMODULE'),
  ('narwhals.stable.v2.selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/stable/v2/selectors.py',
   'PYMODULE'),
  ('narwhals.translate',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/translate.py',
   'PYMODULE'),
  ('narwhals.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/narwhals/typing.py',
   'PYMODULE'),
  ('netrc', '/opt/miniconda3/envs/dist/lib/python3.13/netrc.py', 'PYMODULE'),
  ('nturl2path',
   '/opt/miniconda3/envs/dist/lib/python3.13/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/opt/miniconda3/envs/dist/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('numpy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_core/umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_typing/_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/char/__init__.py',
   'PYMODULE'),
  ('numpy.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/core/_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ctypeslib/__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ctypeslib/_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_backends/_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/f2py/use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.lib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/rec/__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/strings/__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/numpy/version.py',
   'PYMODULE'),
  ('opcode', '/opt/miniconda3/envs/dist/lib/python3.13/opcode.py', 'PYMODULE'),
  ('optparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/optparse.py',
   'PYMODULE'),
  ('packaging',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/packaging/version.py',
   'PYMODULE'),
  ('pandas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/__init__.py',
   'PYMODULE'),
  ('pandas._config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_config/localization.py',
   'PYMODULE'),
  ('pandas._libs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/tslibs/__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_libs/window/__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_testing/contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_typing.py',
   'PYMODULE'),
  ('pandas._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/extensions/__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/types/__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/api/typing/__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/numpy/__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/numpy/function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/compat/pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/_numba/kernels/var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/array_algos/transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/arrow/extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/sparse/scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/arrays/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/base.py',
   'PYMODULE'),
  ('pandas.core.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/computation/scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/dtypes/missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/groupby/ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexers/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexers/objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexers/utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexes/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/interchange/utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/internals/ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/methods/to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/ops/missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/reshape/util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/strings/object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/tools/times.py',
   'PYMODULE'),
  ('pandas.core.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/util/__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/util/hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/util/numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/core/window/rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/errors/__init__.py',
   'PYMODULE'),
  ('pandas.io',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/clipboard/__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/excel/_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/formats/xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/html.py',
   'PYMODULE'),
  ('pandas.io.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/json/_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/parsers/readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sas/sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/io/xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/plotting/__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/plotting/_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/plotting/_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/tseries/offsets.py',
   'PYMODULE'),
  ('pandas.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pandas/util/version/__init__.py',
   'PYMODULE'),
  ('pathlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/opt/miniconda3/envs/dist/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   '/opt/miniconda3/envs/dist/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('pdb', '/opt/miniconda3/envs/dist/lib/python3.13/pdb.py', 'PYMODULE'),
  ('pickle', '/opt/miniconda3/envs/dist/lib/python3.13/pickle.py', 'PYMODULE'),
  ('pickletools',
   '/opt/miniconda3/envs/dist/lib/python3.13/pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/opt/miniconda3/envs/dist/lib/python3.13/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/opt/miniconda3/envs/dist/lib/python3.13/plistlib.py',
   'PYMODULE'),
  ('pprint', '/opt/miniconda3/envs/dist/lib/python3.13/pprint.py', 'PYMODULE'),
  ('py_compile',
   '/opt/miniconda3/envs/dist/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('pyarrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/__init__.py',
   'PYMODULE'),
  ('pyarrow._compute_docstrings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_compute_docstrings.py',
   'PYMODULE'),
  ('pyarrow._generated_version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/_generated_version.py',
   'PYMODULE'),
  ('pyarrow.acero',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/acero.py',
   'PYMODULE'),
  ('pyarrow.benchmark',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/benchmark.py',
   'PYMODULE'),
  ('pyarrow.cffi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/cffi.py',
   'PYMODULE'),
  ('pyarrow.compute',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/compute.py',
   'PYMODULE'),
  ('pyarrow.conftest',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/conftest.py',
   'PYMODULE'),
  ('pyarrow.csv',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/csv.py',
   'PYMODULE'),
  ('pyarrow.cuda',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/cuda.py',
   'PYMODULE'),
  ('pyarrow.dataset',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/dataset.py',
   'PYMODULE'),
  ('pyarrow.feather',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/feather.py',
   'PYMODULE'),
  ('pyarrow.flight',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/flight.py',
   'PYMODULE'),
  ('pyarrow.fs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/fs.py',
   'PYMODULE'),
  ('pyarrow.interchange',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/__init__.py',
   'PYMODULE'),
  ('pyarrow.interchange.buffer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/buffer.py',
   'PYMODULE'),
  ('pyarrow.interchange.column',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/column.py',
   'PYMODULE'),
  ('pyarrow.interchange.dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/dataframe.py',
   'PYMODULE'),
  ('pyarrow.interchange.from_dataframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/interchange/from_dataframe.py',
   'PYMODULE'),
  ('pyarrow.ipc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/ipc.py',
   'PYMODULE'),
  ('pyarrow.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/json.py',
   'PYMODULE'),
  ('pyarrow.jvm',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/jvm.py',
   'PYMODULE'),
  ('pyarrow.orc',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/orc.py',
   'PYMODULE'),
  ('pyarrow.pandas_compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/pandas_compat.py',
   'PYMODULE'),
  ('pyarrow.parquet',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/parquet/__init__.py',
   'PYMODULE'),
  ('pyarrow.parquet.core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/parquet/core.py',
   'PYMODULE'),
  ('pyarrow.parquet.encryption',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/parquet/encryption.py',
   'PYMODULE'),
  ('pyarrow.substrait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/substrait.py',
   'PYMODULE'),
  ('pyarrow.tests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/__init__.py',
   'PYMODULE'),
  ('pyarrow.tests.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/tests/util.py',
   'PYMODULE'),
  ('pyarrow.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/types.py',
   'PYMODULE'),
  ('pyarrow.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/util.py',
   'PYMODULE'),
  ('pyarrow.vendored',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/vendored/__init__.py',
   'PYMODULE'),
  ('pyarrow.vendored.docscrape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/vendored/docscrape.py',
   'PYMODULE'),
  ('pyarrow.vendored.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pyarrow/vendored/version.py',
   'PYMODULE'),
  ('pydeck',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/__init__.py',
   'PYMODULE'),
  ('pydeck._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/_version.py',
   'PYMODULE'),
  ('pydeck.bindings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/__init__.py',
   'PYMODULE'),
  ('pydeck.bindings.base_map_provider',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/base_map_provider.py',
   'PYMODULE'),
  ('pydeck.bindings.deck',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/deck.py',
   'PYMODULE'),
  ('pydeck.bindings.json_tools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/json_tools.py',
   'PYMODULE'),
  ('pydeck.bindings.layer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/layer.py',
   'PYMODULE'),
  ('pydeck.bindings.light_settings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/light_settings.py',
   'PYMODULE'),
  ('pydeck.bindings.map_styles',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/map_styles.py',
   'PYMODULE'),
  ('pydeck.bindings.view',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/view.py',
   'PYMODULE'),
  ('pydeck.bindings.view_state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/bindings/view_state.py',
   'PYMODULE'),
  ('pydeck.data_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/__init__.py',
   'PYMODULE'),
  ('pydeck.data_utils.binary_transfer',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/binary_transfer.py',
   'PYMODULE'),
  ('pydeck.data_utils.color_scales',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/color_scales.py',
   'PYMODULE'),
  ('pydeck.data_utils.type_checking',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/type_checking.py',
   'PYMODULE'),
  ('pydeck.data_utils.viewport_helpers',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/data_utils/viewport_helpers.py',
   'PYMODULE'),
  ('pydeck.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/exceptions/__init__.py',
   'PYMODULE'),
  ('pydeck.exceptions.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/exceptions/exceptions.py',
   'PYMODULE'),
  ('pydeck.frontend_semver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/frontend_semver.py',
   'PYMODULE'),
  ('pydeck.io',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/io/__init__.py',
   'PYMODULE'),
  ('pydeck.io.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/io/html.py',
   'PYMODULE'),
  ('pydeck.nbextension',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/nbextension/__init__.py',
   'PYMODULE'),
  ('pydeck.settings',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/settings.py',
   'PYMODULE'),
  ('pydeck.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/__init__.py',
   'PYMODULE'),
  ('pydeck.types.base',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/base.py',
   'PYMODULE'),
  ('pydeck.types.function',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/function.py',
   'PYMODULE'),
  ('pydeck.types.image',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/image.py',
   'PYMODULE'),
  ('pydeck.types.string',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/types/string.py',
   'PYMODULE'),
  ('pydeck.widget',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/__init__.py',
   'PYMODULE'),
  ('pydeck.widget._frontend',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/_frontend.py',
   'PYMODULE'),
  ('pydeck.widget.debounce',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/debounce.py',
   'PYMODULE'),
  ('pydeck.widget.widget',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pydeck/widget/widget.py',
   'PYMODULE'),
  ('pydoc', '/opt/miniconda3/envs/dist/lib/python3.13/pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/pydoc_data/__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/opt/miniconda3/envs/dist/lib/python3.13/pydoc_data/topics.py',
   'PYMODULE'),
  ('pytz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/pytz/tzinfo.py',
   'PYMODULE'),
  ('queue', '/opt/miniconda3/envs/dist/lib/python3.13/queue.py', 'PYMODULE'),
  ('quopri', '/opt/miniconda3/envs/dist/lib/python3.13/quopri.py', 'PYMODULE'),
  ('random', '/opt/miniconda3/envs/dist/lib/python3.13/random.py', 'PYMODULE'),
  ('referencing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/__init__.py',
   'PYMODULE'),
  ('referencing._attrs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/_attrs.py',
   'PYMODULE'),
  ('referencing._core',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/_core.py',
   'PYMODULE'),
  ('referencing.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/exceptions.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/referencing/typing.py',
   'PYMODULE'),
  ('requests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('requests.api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.auth',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests.certs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/certs.py',
   'PYMODULE'),
  ('requests.compat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/compat.py',
   'PYMODULE'),
  ('requests.cookies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.models',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/models.py',
   'PYMODULE'),
  ('requests.packages',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/packages.py',
   'PYMODULE'),
  ('requests.sessions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/requests/utils.py',
   'PYMODULE'),
  ('rlcompleter',
   '/opt/miniconda3/envs/dist/lib/python3.13/rlcompleter.py',
   'PYMODULE'),
  ('rpds',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/rpds/__init__.py',
   'PYMODULE'),
  ('runpy', '/opt/miniconda3/envs/dist/lib/python3.13/runpy.py', 'PYMODULE'),
  ('secrets',
   '/opt/miniconda3/envs/dist/lib/python3.13/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/opt/miniconda3/envs/dist/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('shlex', '/opt/miniconda3/envs/dist/lib/python3.13/shlex.py', 'PYMODULE'),
  ('shutil', '/opt/miniconda3/envs/dist/lib/python3.13/shutil.py', 'PYMODULE'),
  ('signal', '/opt/miniconda3/envs/dist/lib/python3.13/signal.py', 'PYMODULE'),
  ('six',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/six.py',
   'PYMODULE'),
  ('smmap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/__init__.py',
   'PYMODULE'),
  ('smmap.buf',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/buf.py',
   'PYMODULE'),
  ('smmap.mman',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/mman.py',
   'PYMODULE'),
  ('smmap.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/smmap/util.py',
   'PYMODULE'),
  ('smtplib',
   '/opt/miniconda3/envs/dist/lib/python3.13/smtplib.py',
   'PYMODULE'),
  ('socket', '/opt/miniconda3/envs/dist/lib/python3.13/socket.py', 'PYMODULE'),
  ('socketserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/opt/miniconda3/envs/dist/lib/python3.13/sqlite3/dump.py',
   'PYMODULE'),
  ('ssl', '/opt/miniconda3/envs/dist/lib/python3.13/ssl.py', 'PYMODULE'),
  ('statistics',
   '/opt/miniconda3/envs/dist/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('streamlit',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/__init__.py',
   'PYMODULE'),
  ('streamlit.auth_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/auth_util.py',
   'PYMODULE'),
  ('streamlit.cli_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/cli_util.py',
   'PYMODULE'),
  ('streamlit.column_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/column_config.py',
   'PYMODULE'),
  ('streamlit.commands',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/__init__.py',
   'PYMODULE'),
  ('streamlit.commands.echo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/echo.py',
   'PYMODULE'),
  ('streamlit.commands.execution_control',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/execution_control.py',
   'PYMODULE'),
  ('streamlit.commands.experimental_query_params',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/experimental_query_params.py',
   'PYMODULE'),
  ('streamlit.commands.logo',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/logo.py',
   'PYMODULE'),
  ('streamlit.commands.navigation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/navigation.py',
   'PYMODULE'),
  ('streamlit.commands.page_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/commands/page_config.py',
   'PYMODULE'),
  ('streamlit.components',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/__init__.py',
   'PYMODULE'),
  ('streamlit.components.lib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/lib/__init__.py',
   'PYMODULE'),
  ('streamlit.components.lib.local_component_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/lib/local_component_registry.py',
   'PYMODULE'),
  ('streamlit.components.types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/types/__init__.py',
   'PYMODULE'),
  ('streamlit.components.types.base_component_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/types/base_component_registry.py',
   'PYMODULE'),
  ('streamlit.components.types.base_custom_component',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/types/base_custom_component.py',
   'PYMODULE'),
  ('streamlit.components.v1',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/__init__.py',
   'PYMODULE'),
  ('streamlit.components.v1.component_arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/component_arrow.py',
   'PYMODULE'),
  ('streamlit.components.v1.component_registry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/component_registry.py',
   'PYMODULE'),
  ('streamlit.components.v1.components',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/components.py',
   'PYMODULE'),
  ('streamlit.components.v1.custom_component',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/components/v1/custom_component.py',
   'PYMODULE'),
  ('streamlit.config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/config.py',
   'PYMODULE'),
  ('streamlit.config_option',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/config_option.py',
   'PYMODULE'),
  ('streamlit.config_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/config_util.py',
   'PYMODULE'),
  ('streamlit.connections',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/__init__.py',
   'PYMODULE'),
  ('streamlit.connections.base_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/base_connection.py',
   'PYMODULE'),
  ('streamlit.connections.snowflake_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/snowflake_connection.py',
   'PYMODULE'),
  ('streamlit.connections.snowpark_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/snowpark_connection.py',
   'PYMODULE'),
  ('streamlit.connections.sql_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/sql_connection.py',
   'PYMODULE'),
  ('streamlit.connections.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/connections/util.py',
   'PYMODULE'),
  ('streamlit.cursor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/cursor.py',
   'PYMODULE'),
  ('streamlit.dataframe_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/dataframe_util.py',
   'PYMODULE'),
  ('streamlit.delta_generator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/delta_generator.py',
   'PYMODULE'),
  ('streamlit.delta_generator_singletons',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/delta_generator_singletons.py',
   'PYMODULE'),
  ('streamlit.deprecation_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/deprecation_util.py',
   'PYMODULE'),
  ('streamlit.development',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/development.py',
   'PYMODULE'),
  ('streamlit.elements',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.alert',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/alert.py',
   'PYMODULE'),
  ('streamlit.elements.arrow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/arrow.py',
   'PYMODULE'),
  ('streamlit.elements.balloons',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/balloons.py',
   'PYMODULE'),
  ('streamlit.elements.bokeh_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/bokeh_chart.py',
   'PYMODULE'),
  ('streamlit.elements.code',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/code.py',
   'PYMODULE'),
  ('streamlit.elements.deck_gl_json_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/deck_gl_json_chart.py',
   'PYMODULE'),
  ('streamlit.elements.dialog_decorator',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/dialog_decorator.py',
   'PYMODULE'),
  ('streamlit.elements.doc_string',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/doc_string.py',
   'PYMODULE'),
  ('streamlit.elements.empty',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/empty.py',
   'PYMODULE'),
  ('streamlit.elements.exception',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/exception.py',
   'PYMODULE'),
  ('streamlit.elements.form',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/form.py',
   'PYMODULE'),
  ('streamlit.elements.graphviz_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/graphviz_chart.py',
   'PYMODULE'),
  ('streamlit.elements.heading',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/heading.py',
   'PYMODULE'),
  ('streamlit.elements.html',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/html.py',
   'PYMODULE'),
  ('streamlit.elements.iframe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/iframe.py',
   'PYMODULE'),
  ('streamlit.elements.image',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/image.py',
   'PYMODULE'),
  ('streamlit.elements.json',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/json.py',
   'PYMODULE'),
  ('streamlit.elements.layouts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/layouts.py',
   'PYMODULE'),
  ('streamlit.elements.lib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.lib.built_in_chart_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/built_in_chart_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.color_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/color_util.py',
   'PYMODULE'),
  ('streamlit.elements.lib.column_config_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/column_config_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.column_types',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/column_types.py',
   'PYMODULE'),
  ('streamlit.elements.lib.dialog',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/dialog.py',
   'PYMODULE'),
  ('streamlit.elements.lib.dicttools',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/dicttools.py',
   'PYMODULE'),
  ('streamlit.elements.lib.file_uploader_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/file_uploader_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.form_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/form_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.image_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/image_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.js_number',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/js_number.py',
   'PYMODULE'),
  ('streamlit.elements.lib.layout_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/layout_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.mutable_status_container',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/mutable_status_container.py',
   'PYMODULE'),
  ('streamlit.elements.lib.options_selector_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/options_selector_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.pandas_styler_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/pandas_styler_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.policies',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/policies.py',
   'PYMODULE'),
  ('streamlit.elements.lib.streamlit_plotly_theme',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/streamlit_plotly_theme.py',
   'PYMODULE'),
  ('streamlit.elements.lib.subtitle_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/subtitle_utils.py',
   'PYMODULE'),
  ('streamlit.elements.lib.utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/lib/utils.py',
   'PYMODULE'),
  ('streamlit.elements.map',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/map.py',
   'PYMODULE'),
  ('streamlit.elements.markdown',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/markdown.py',
   'PYMODULE'),
  ('streamlit.elements.media',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/media.py',
   'PYMODULE'),
  ('streamlit.elements.metric',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/metric.py',
   'PYMODULE'),
  ('streamlit.elements.plotly_chart',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/plotly_chart.py',
   'PYMODULE'),
  ('streamlit.elements.progress',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/progress.py',
   'PYMODULE'),
  ('streamlit.elements.pyplot',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/pyplot.py',
   'PYMODULE'),
  ('streamlit.elements.snow',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/snow.py',
   'PYMODULE'),
  ('streamlit.elements.spinner',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/spinner.py',
   'PYMODULE'),
  ('streamlit.elements.text',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/text.py',
   'PYMODULE'),
  ('streamlit.elements.toast',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/toast.py',
   'PYMODULE'),
  ('streamlit.elements.vega_charts',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/vega_charts.py',
   'PYMODULE'),
  ('streamlit.elements.widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/__init__.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.audio_input',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/audio_input.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.button',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/button.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.button_group',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/button_group.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.camera_input',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/camera_input.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.chat',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/chat.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.checkbox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/checkbox.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.color_picker',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/color_picker.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.data_editor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/data_editor.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.file_uploader',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/file_uploader.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.multiselect',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/multiselect.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.number_input',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/number_input.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.radio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/radio.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.select_slider',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/select_slider.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.selectbox',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/selectbox.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.slider',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/slider.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.text_widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/text_widgets.py',
   'PYMODULE'),
  ('streamlit.elements.widgets.time_widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/widgets/time_widgets.py',
   'PYMODULE'),
  ('streamlit.elements.write',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/elements/write.py',
   'PYMODULE'),
  ('streamlit.emojis',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/emojis.py',
   'PYMODULE'),
  ('streamlit.env_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/env_util.py',
   'PYMODULE'),
  ('streamlit.error_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/error_util.py',
   'PYMODULE'),
  ('streamlit.errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/errors.py',
   'PYMODULE'),
  ('streamlit.file_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/file_util.py',
   'PYMODULE'),
  ('streamlit.git_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/git_util.py',
   'PYMODULE'),
  ('streamlit.hello',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/hello/__init__.py',
   'PYMODULE'),
  ('streamlit.hello.streamlit_app',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/hello/streamlit_app.py',
   'PYMODULE'),
  ('streamlit.logger',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/logger.py',
   'PYMODULE'),
  ('streamlit.material_icon_names',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/material_icon_names.py',
   'PYMODULE'),
  ('streamlit.navigation',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/navigation/__init__.py',
   'PYMODULE'),
  ('streamlit.navigation.page',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/navigation/page.py',
   'PYMODULE'),
  ('streamlit.net_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/net_util.py',
   'PYMODULE'),
  ('streamlit.proto',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/__init__.py',
   'PYMODULE'),
  ('streamlit.proto.Alert_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Alert_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AppPage_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AppPage_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ArrowNamedDataSet_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ArrowNamedDataSet_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ArrowVegaLiteChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ArrowVegaLiteChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Arrow_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Arrow_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AudioInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AudioInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Audio_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Audio_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AuthRedirect_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AuthRedirect_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.AutoRerun_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/AutoRerun_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.BackMsg_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/BackMsg_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Balloons_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Balloons_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Block_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Block_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.BokehChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/BokehChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ButtonGroup_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ButtonGroup_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Button_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Button_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.CameraInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/CameraInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ChatInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ChatInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Checkbox_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Checkbox_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ClientState_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ClientState_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Code_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Code_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ColorPicker_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ColorPicker_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Common_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Common_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Components_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Components_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DataFrame_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DataFrame_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DateInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DateInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DeckGlJsonChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DeckGlJsonChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Delta_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Delta_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DocString_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DocString_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.DownloadButton_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/DownloadButton_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Element_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Element_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Empty_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Empty_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Exception_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Exception_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Favicon_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Favicon_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.FileUploader_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/FileUploader_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ForwardMsg_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ForwardMsg_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.GapSize_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GapSize_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.GitInfo_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GitInfo_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.GraphVizChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/GraphVizChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Heading_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Heading_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.HeightConfig_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/HeightConfig_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Html_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Html_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.IFrame_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/IFrame_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Image_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Image_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Json_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Json_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.LabelVisibilityMessage_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/LabelVisibilityMessage_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.LinkButton_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/LinkButton_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Logo_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Logo_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Markdown_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Markdown_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Metric_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Metric_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.MultiSelect_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/MultiSelect_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.NamedDataSet_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NamedDataSet_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Navigation_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Navigation_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.NewSession_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NewSession_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.NumberInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/NumberInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageConfig_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageConfig_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageInfo_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageInfo_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageLink_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageLink_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageNotFound_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageNotFound_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PageProfile_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PageProfile_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PagesChanged_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PagesChanged_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.ParentMessage_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/ParentMessage_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.PlotlyChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/PlotlyChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Progress_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Progress_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Radio_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Radio_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.RootContainer_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/RootContainer_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Selectbox_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Selectbox_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.SessionEvent_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/SessionEvent_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.SessionStatus_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/SessionStatus_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Skeleton_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Skeleton_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Slider_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Slider_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Snow_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Snow_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Spinner_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Spinner_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.TextArea_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TextArea_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.TextInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TextInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Text_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Text_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.TimeInput_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/TimeInput_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Toast_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Toast_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.VegaLiteChart_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/VegaLiteChart_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.Video_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/Video_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.WidgetStates_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/WidgetStates_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.WidthConfig_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/WidthConfig_pb2.py',
   'PYMODULE'),
  ('streamlit.proto.openmetrics_data_model_pb2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/proto/openmetrics_data_model_pb2.py',
   'PYMODULE'),
  ('streamlit.runtime',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.app_session',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/app_session.py',
   'PYMODULE'),
  ('streamlit.runtime.caching',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_data_api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_data_api.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_errors',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_errors.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_resource_api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_resource_api.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_type',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_type.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cache_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cache_utils.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.cached_message_replay',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/cached_message_replay.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.hashing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/hashing.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.legacy_cache_api',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/legacy_cache_api.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.cache_storage_protocol',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/cache_storage_protocol.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.dummy_cache_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/dummy_cache_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.in_memory_cache_storage_wrapper',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/in_memory_cache_storage_wrapper.py',
   'PYMODULE'),
  ('streamlit.runtime.caching.storage.local_disk_cache_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/caching/storage/local_disk_cache_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.connection_factory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/connection_factory.py',
   'PYMODULE'),
  ('streamlit.runtime.context',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/context.py',
   'PYMODULE'),
  ('streamlit.runtime.context_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/context_util.py',
   'PYMODULE'),
  ('streamlit.runtime.credentials',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/credentials.py',
   'PYMODULE'),
  ('streamlit.runtime.forward_msg_cache',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/forward_msg_cache.py',
   'PYMODULE'),
  ('streamlit.runtime.forward_msg_queue',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/forward_msg_queue.py',
   'PYMODULE'),
  ('streamlit.runtime.fragment',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/fragment.py',
   'PYMODULE'),
  ('streamlit.runtime.media_file_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/media_file_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.media_file_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/media_file_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.memory_media_file_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/memory_media_file_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.memory_session_storage',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/memory_session_storage.py',
   'PYMODULE'),
  ('streamlit.runtime.memory_uploaded_file_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/memory_uploaded_file_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.metrics_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/metrics_util.py',
   'PYMODULE'),
  ('streamlit.runtime.pages_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/pages_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.runtime',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/runtime.py',
   'PYMODULE'),
  ('streamlit.runtime.runtime_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/runtime_util.py',
   'PYMODULE'),
  ('streamlit.runtime.script_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/script_data.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.exec_code',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/exec_code.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.magic',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/magic.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.magic_funcs',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/magic_funcs.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.script_cache',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/script_cache.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner.script_runner',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner/script_runner.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/exceptions.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils.script_requests',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/script_requests.py',
   'PYMODULE'),
  ('streamlit.runtime.scriptrunner_utils.script_run_context',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/scriptrunner_utils/script_run_context.py',
   'PYMODULE'),
  ('streamlit.runtime.secrets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/secrets.py',
   'PYMODULE'),
  ('streamlit.runtime.session_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/session_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/__init__.py',
   'PYMODULE'),
  ('streamlit.runtime.state.common',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/common.py',
   'PYMODULE'),
  ('streamlit.runtime.state.query_params',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/query_params.py',
   'PYMODULE'),
  ('streamlit.runtime.state.query_params_proxy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/query_params_proxy.py',
   'PYMODULE'),
  ('streamlit.runtime.state.safe_session_state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/safe_session_state.py',
   'PYMODULE'),
  ('streamlit.runtime.state.session_state',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/session_state.py',
   'PYMODULE'),
  ('streamlit.runtime.state.session_state_proxy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/session_state_proxy.py',
   'PYMODULE'),
  ('streamlit.runtime.state.widgets',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/state/widgets.py',
   'PYMODULE'),
  ('streamlit.runtime.stats',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/stats.py',
   'PYMODULE'),
  ('streamlit.runtime.uploaded_file_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/uploaded_file_manager.py',
   'PYMODULE'),
  ('streamlit.runtime.websocket_session_manager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/runtime/websocket_session_manager.py',
   'PYMODULE'),
  ('streamlit.source_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/source_util.py',
   'PYMODULE'),
  ('streamlit.string_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/string_util.py',
   'PYMODULE'),
  ('streamlit.temporary_directory',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/temporary_directory.py',
   'PYMODULE'),
  ('streamlit.time_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/time_util.py',
   'PYMODULE'),
  ('streamlit.type_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/type_util.py',
   'PYMODULE'),
  ('streamlit.url_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/url_util.py',
   'PYMODULE'),
  ('streamlit.user_info',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/user_info.py',
   'PYMODULE'),
  ('streamlit.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/util.py',
   'PYMODULE'),
  ('streamlit.vendor',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/vendor/__init__.py',
   'PYMODULE'),
  ('streamlit.vendor.pympler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/vendor/pympler/__init__.py',
   'PYMODULE'),
  ('streamlit.vendor.pympler.asizeof',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/vendor/pympler/asizeof.py',
   'PYMODULE'),
  ('streamlit.version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/version.py',
   'PYMODULE'),
  ('streamlit.watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/__init__.py',
   'PYMODULE'),
  ('streamlit.watcher.event_based_path_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/event_based_path_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.folder_black_list',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/folder_black_list.py',
   'PYMODULE'),
  ('streamlit.watcher.local_sources_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/local_sources_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.path_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/path_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.polling_path_watcher',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/polling_path_watcher.py',
   'PYMODULE'),
  ('streamlit.watcher.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/watcher/util.py',
   'PYMODULE'),
  ('streamlit.web',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/__init__.py',
   'PYMODULE'),
  ('streamlit.web.bootstrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/bootstrap.py',
   'PYMODULE'),
  ('streamlit.web.cache_storage_manager_config',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/cache_storage_manager_config.py',
   'PYMODULE'),
  ('streamlit.web.cli',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/cli.py',
   'PYMODULE'),
  ('streamlit.web.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/__init__.py',
   'PYMODULE'),
  ('streamlit.web.server.app_static_file_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/app_static_file_handler.py',
   'PYMODULE'),
  ('streamlit.web.server.authlib_tornado_integration',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/authlib_tornado_integration.py',
   'PYMODULE'),
  ('streamlit.web.server.browser_websocket_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/browser_websocket_handler.py',
   'PYMODULE'),
  ('streamlit.web.server.component_request_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/component_request_handler.py',
   'PYMODULE'),
  ('streamlit.web.server.media_file_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/media_file_handler.py',
   'PYMODULE'),
  ('streamlit.web.server.oauth_authlib_routes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/oauth_authlib_routes.py',
   'PYMODULE'),
  ('streamlit.web.server.oidc_mixin',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/oidc_mixin.py',
   'PYMODULE'),
  ('streamlit.web.server.routes',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/routes.py',
   'PYMODULE'),
  ('streamlit.web.server.server',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/server.py',
   'PYMODULE'),
  ('streamlit.web.server.server_util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/server_util.py',
   'PYMODULE'),
  ('streamlit.web.server.stats_request_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/stats_request_handler.py',
   'PYMODULE'),
  ('streamlit.web.server.upload_file_request_handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/streamlit/web/server/upload_file_request_handler.py',
   'PYMODULE'),
  ('string', '/opt/miniconda3/envs/dist/lib/python3.13/string.py', 'PYMODULE'),
  ('stringprep',
   '/opt/miniconda3/envs/dist/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/opt/miniconda3/envs/dist/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   '/opt/miniconda3/envs/dist/lib/python3.13/sysconfig/__init__.py',
   'PYMODULE'),
  ('tarfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('tenacity',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/__init__.py',
   'PYMODULE'),
  ('tenacity._utils',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/_utils.py',
   'PYMODULE'),
  ('tenacity.after',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/after.py',
   'PYMODULE'),
  ('tenacity.asyncio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/asyncio/__init__.py',
   'PYMODULE'),
  ('tenacity.asyncio.retry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/asyncio/retry.py',
   'PYMODULE'),
  ('tenacity.before',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/before.py',
   'PYMODULE'),
  ('tenacity.before_sleep',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/before_sleep.py',
   'PYMODULE'),
  ('tenacity.nap',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/nap.py',
   'PYMODULE'),
  ('tenacity.retry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/retry.py',
   'PYMODULE'),
  ('tenacity.stop',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/stop.py',
   'PYMODULE'),
  ('tenacity.tornadoweb',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/tornadoweb.py',
   'PYMODULE'),
  ('tenacity.wait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tenacity/wait.py',
   'PYMODULE'),
  ('textwrap',
   '/opt/miniconda3/envs/dist/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/opt/miniconda3/envs/dist/lib/python3.13/threading.py',
   'PYMODULE'),
  ('timeit', '/opt/miniconda3/envs/dist/lib/python3.13/timeit.py', 'PYMODULE'),
  ('token', '/opt/miniconda3/envs/dist/lib/python3.13/token.py', 'PYMODULE'),
  ('tokenize',
   '/opt/miniconda3/envs/dist/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('toml',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/__init__.py',
   'PYMODULE'),
  ('toml.decoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/decoder.py',
   'PYMODULE'),
  ('toml.encoder',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/encoder.py',
   'PYMODULE'),
  ('toml.tz',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/toml/tz.py',
   'PYMODULE'),
  ('tornado',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/__init__.py',
   'PYMODULE'),
  ('tornado._locale_data',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/_locale_data.py',
   'PYMODULE'),
  ('tornado.auth',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/auth.py',
   'PYMODULE'),
  ('tornado.autoreload',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/autoreload.py',
   'PYMODULE'),
  ('tornado.concurrent',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/concurrent.py',
   'PYMODULE'),
  ('tornado.curl_httpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/curl_httpclient.py',
   'PYMODULE'),
  ('tornado.escape',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/escape.py',
   'PYMODULE'),
  ('tornado.gen',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/http1connection.py',
   'PYMODULE'),
  ('tornado.httpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/httpclient.py',
   'PYMODULE'),
  ('tornado.httpserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/iostream.py',
   'PYMODULE'),
  ('tornado.locale',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/locale.py',
   'PYMODULE'),
  ('tornado.locks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/locks.py',
   'PYMODULE'),
  ('tornado.log',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/log.py',
   'PYMODULE'),
  ('tornado.netutil',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/netutil.py',
   'PYMODULE'),
  ('tornado.options',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/options.py',
   'PYMODULE'),
  ('tornado.platform',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/platform/__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/platform/asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/process.py',
   'PYMODULE'),
  ('tornado.queues',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/queues.py',
   'PYMODULE'),
  ('tornado.routing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/routing.py',
   'PYMODULE'),
  ('tornado.simple_httpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/simple_httpclient.py',
   'PYMODULE'),
  ('tornado.tcpclient',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/tcpclient.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/tcpserver.py',
   'PYMODULE'),
  ('tornado.template',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/template.py',
   'PYMODULE'),
  ('tornado.testing',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/testing.py',
   'PYMODULE'),
  ('tornado.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/util.py',
   'PYMODULE'),
  ('tornado.web',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/web.py',
   'PYMODULE'),
  ('tornado.websocket',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/websocket.py',
   'PYMODULE'),
  ('tornado.wsgi',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/tornado/wsgi.py',
   'PYMODULE'),
  ('tracemalloc',
   '/opt/miniconda3/envs/dist/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('tty', '/opt/miniconda3/envs/dist/lib/python3.13/tty.py', 'PYMODULE'),
  ('typing', '/opt/miniconda3/envs/dist/lib/python3.13/typing.py', 'PYMODULE'),
  ('typing_extensions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('ui',
   '/Users/<USER>/Library/CloudStorage/OneDrive-个人/TechMax工作文件/Python开发/dist/ui.py',
   'PYMODULE'),
  ('unittest',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/__init__.py',
   'PYMODULE'),
  ('unittest._log',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.case',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/case.py',
   'PYMODULE'),
  ('unittest.loader',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/loader.py',
   'PYMODULE'),
  ('unittest.main',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/main.py',
   'PYMODULE'),
  ('unittest.result',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/result.py',
   'PYMODULE'),
  ('unittest.runner',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/runner.py',
   'PYMODULE'),
  ('unittest.signals',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/signals.py',
   'PYMODULE'),
  ('unittest.suite',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/suite.py',
   'PYMODULE'),
  ('unittest.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/unittest/util.py',
   'PYMODULE'),
  ('urllib',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/urllib/response.py',
   'PYMODULE'),
  ('urllib3',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/opt/miniconda3/envs/dist/lib/python3.13/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('uuid', '/opt/miniconda3/envs/dist/lib/python3.13/uuid.py', 'PYMODULE'),
  ('wave', '/opt/miniconda3/envs/dist/lib/python3.13/wave.py', 'PYMODULE'),
  ('webbrowser',
   '/opt/miniconda3/envs/dist/lib/python3.13/webbrowser.py',
   'PYMODULE'),
  ('xml',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/__init__.py',
   'PYMODULE'),
  ('xml.dom',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/opt/miniconda3/envs/dist/lib/python3.13/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/opt/miniconda3/envs/dist/lib/python3.13/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/opt/miniconda3/envs/dist/lib/python3.13/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('zipimport',
   '/opt/miniconda3/envs/dist/lib/python3.13/zipimport.py',
   'PYMODULE')])
