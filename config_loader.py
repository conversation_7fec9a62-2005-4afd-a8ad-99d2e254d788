import json
import os
import sys


def get_resource_path(relative_path):
    """获取资源文件的绝对路径，兼容开发环境和打包环境"""
    if getattr(sys, 'frozen', False):
        # 在打包环境中，使用程序所在目录
        base_path = getattr(sys, '_MEIPASS', os.path.dirname(sys.executable))
    else:
        # 在开发环境中，使用当前工作目录
        base_path = os.getcwd()
    
    return os.path.join(base_path, relative_path)


def load_config(config_file='config.json'):
    """加载配置文件"""
    try:
        config_path = get_resource_path(config_file)
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到，使用默认配置")
        return get_default_config()
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}，使用默认配置")
        return get_default_config()


def get_default_config():
    """默认配置"""
    return {
        "row_count": 50,
        "app": {
            "title": "My Streamlit App",
            "port": 8501
        },
        "data": {
            "sample_size": 100,
            "mean_a": 55,
            "std_a": 12,
            "mean_b": 66,
            "std_b": 12
        }
    }


def save_config(config, config_file='config.json'):
    """保存配置文件（仅在开发环境中有效）"""
    if not getattr(sys, 'frozen', False):
        # 只在开发环境中允许保存配置
        config_path = get_resource_path(config_file)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print(f"配置已保存到 {config_path}")
    else:
        print("打包环境中无法修改配置文件")
