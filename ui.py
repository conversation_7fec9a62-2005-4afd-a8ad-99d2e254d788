import streamlit as st
import pandas as pd
import numpy as np
from config_loader import load_config


def main():
    # 加载配置
    config = load_config()

    # 设置页面标题
    st.title("配置驱动的Streamlit应用")
    

    # 从配置获取参数
    size = config.get("row_count", 100)

    # 显示配置信息
    st.sidebar.header("当前配置")
    st.sidebar.json(config)

    # 生成数据
    a = np.random.normal(55, 12, size)
    b = np.random.normal(66, 12, size)
    df = pd.DataFrame({"A": a, "B": b})

    # 显示数据
    st.subheader(f"随机数据 (行数: {size})")
    st.dataframe(df)

    # 显示统计信息
    st.subheader("数据统计")
    col1, col2 = st.columns(2)
    with col1:
        st.metric("A列均值", f"{df['A'].mean():.2f}")
        st.metric("A列标准差", f"{df['A'].std():.2f}")
    with col2:
        st.metric("B列均值", f"{df['B'].mean():.2f}")
        st.metric("B列标准差", f"{df['B'].std():.2f}")

if __name__ == "__main__":
    main()
