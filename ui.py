import streamlit as st
import pandas as pd
import numpy as np
import json


def load_config():
    with open("config.json", 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config


def main():
    config = load_config()
    size = config.get("row_count", 100)

    a = np.random.normal(55, 12, size)
    b = np.random.normal(66, 12, size)
    df = pd.DataFrame({"A": a, "B": b})
    st.dataframe(df)

if __name__ == "__main__":
    main()
